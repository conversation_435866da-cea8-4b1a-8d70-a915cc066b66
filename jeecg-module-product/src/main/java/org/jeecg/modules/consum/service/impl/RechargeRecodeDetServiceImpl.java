package org.jeecg.modules.consum.service.impl;

import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import org.jeecg.modules.consum.mapper.RechargeRecodeDetMapper;
import org.jeecg.modules.consum.service.IRechargeRecodeDetService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Service
public class RechargeRecodeDetServiceImpl extends ServiceImpl<RechargeRecodeDetMapper, RechargeRecodeDet> implements IRechargeRecodeDetService {

}
