package org.jeecg.modules.consum.service.impl;

import org.jeecg.modules.consum.entity.RechargeRecode;
import org.jeecg.modules.consum.mapper.RechargeRecodeMapper;
import org.jeecg.modules.consum.service.IRechargeRecodeService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Service
public class RechargeRecodeServiceImpl extends ServiceImpl<RechargeRecodeMapper, RechargeRecode> implements IRechargeRecodeService {

}
