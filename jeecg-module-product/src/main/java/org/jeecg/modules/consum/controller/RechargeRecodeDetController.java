package org.jeecg.modules.consum.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import org.jeecg.modules.consum.service.IRechargeRecodeDetService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 测试环境用消耗明细
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Api(tags="测试环境用消耗明细")
@RestController
@RequestMapping("/consum/rechargeRecodeDet")
@Slf4j
public class RechargeRecodeDetController extends JeecgController<RechargeRecodeDet, IRechargeRecodeDetService> {
	@Autowired
	private IRechargeRecodeDetService rechargeRecodeDetService;
	
	/**
	 * 分页列表查询
	 *
	 * @param rechargeRecodeDet
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "测试环境用消耗明细-分页列表查询")
	@ApiOperation(value="测试环境用消耗明细-分页列表查询", notes="测试环境用消耗明细-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<RechargeRecodeDet>> queryPageList(RechargeRecodeDet rechargeRecodeDet,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<RechargeRecodeDet> queryWrapper = QueryGenerator.initQueryWrapper(rechargeRecodeDet, req.getParameterMap());
		Page<RechargeRecodeDet> page = new Page<RechargeRecodeDet>(pageNo, pageSize);
		IPage<RechargeRecodeDet> pageList = rechargeRecodeDetService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rechargeRecodeDet
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-添加")
	@ApiOperation(value="测试环境用消耗明细-添加", notes="测试环境用消耗明细-添加")
	@RequiresPermissions("consum:recharge_recode_det:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RechargeRecodeDet rechargeRecodeDet) {
		rechargeRecodeDetService.save(rechargeRecodeDet);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rechargeRecodeDet
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-编辑")
	@ApiOperation(value="测试环境用消耗明细-编辑", notes="测试环境用消耗明细-编辑")
	@RequiresPermissions("consum:recharge_recode_det:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RechargeRecodeDet rechargeRecodeDet) {
		rechargeRecodeDetService.updateById(rechargeRecodeDet);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-通过id删除")
	@ApiOperation(value="测试环境用消耗明细-通过id删除", notes="测试环境用消耗明细-通过id删除")
	@RequiresPermissions("consum:recharge_recode_det:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rechargeRecodeDetService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "测试环境用消耗明细-批量删除")
	@ApiOperation(value="测试环境用消耗明细-批量删除", notes="测试环境用消耗明细-批量删除")
	@RequiresPermissions("consum:recharge_recode_det:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rechargeRecodeDetService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "测试环境用消耗明细-通过id查询")
	@ApiOperation(value="测试环境用消耗明细-通过id查询", notes="测试环境用消耗明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RechargeRecodeDet> queryById(@RequestParam(name="id",required=true) String id) {
		RechargeRecodeDet rechargeRecodeDet = rechargeRecodeDetService.getById(id);
		if(rechargeRecodeDet==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rechargeRecodeDet);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rechargeRecodeDet
    */
    @RequiresPermissions("consum:recharge_recode_det:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RechargeRecodeDet rechargeRecodeDet) {
        return super.exportXls(request, rechargeRecodeDet, RechargeRecodeDet.class, "测试环境用消耗明细");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("consum:recharge_recode_det:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RechargeRecodeDet.class);
    }

}
