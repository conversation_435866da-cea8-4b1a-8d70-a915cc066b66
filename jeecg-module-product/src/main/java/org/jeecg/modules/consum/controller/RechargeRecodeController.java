package org.jeecg.modules.consum.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.consum.entity.RechargeRecode;
import org.jeecg.modules.consum.service.IRechargeRecodeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.info.service.TenantFilter;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 模拟充值记录
 * @Author: jeecg-boot
 * @Date:   2025-08-27
 * @Version: V1.0
 */
@Api(tags="模拟充值记录")
@RestController
@RequestMapping("/consum/rechargeRecode")
@Slf4j
public class RechargeRecodeController extends JeecgController<RechargeRecode, IRechargeRecodeService> {
	@Autowired
	private IRechargeRecodeService rechargeRecodeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param rechargeRecode
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "模拟充值记录-分页列表查询")
	@ApiOperation(value="模拟充值记录-分页列表查询", notes="模拟充值记录-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<RechargeRecode>> queryPageList(RechargeRecode rechargeRecode,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<RechargeRecode> queryWrapper = QueryGenerator.initQueryWrapper(rechargeRecode, req.getParameterMap());
		Page<RechargeRecode> page = new Page<RechargeRecode>(pageNo, pageSize);
		IPage<RechargeRecode> pageList = rechargeRecodeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param rechargeRecode
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-添加")
	@ApiOperation(value="模拟充值记录-添加", notes="模拟充值记录-添加")
	@RequiresPermissions("consum:recharge_recode:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody RechargeRecode rechargeRecode) {
		rechargeRecodeService.save(rechargeRecode);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param rechargeRecode
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-编辑")
	@ApiOperation(value="模拟充值记录-编辑", notes="模拟充值记录-编辑")
	@RequiresPermissions("consum:recharge_recode:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody RechargeRecode rechargeRecode) {
		rechargeRecodeService.updateById(rechargeRecode);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-通过id删除")
	@ApiOperation(value="模拟充值记录-通过id删除", notes="模拟充值记录-通过id删除")
	@RequiresPermissions("consum:recharge_recode:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		rechargeRecodeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "模拟充值记录-批量删除")
	@ApiOperation(value="模拟充值记录-批量删除", notes="模拟充值记录-批量删除")
	@RequiresPermissions("consum:recharge_recode:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.rechargeRecodeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "模拟充值记录-通过id查询")
	@ApiOperation(value="模拟充值记录-通过id查询", notes="模拟充值记录-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<RechargeRecode> queryById(@RequestParam(name="id",required=true) String id) {
		RechargeRecode rechargeRecode = rechargeRecodeService.getById(id);
		if(rechargeRecode==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(rechargeRecode);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param rechargeRecode
    */
    @RequiresPermissions("consum:recharge_recode:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, RechargeRecode rechargeRecode) {
        return super.exportXls(request, rechargeRecode, RechargeRecode.class, "模拟充值记录");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("consum:recharge_recode:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, RechargeRecode.class);
    }

}
