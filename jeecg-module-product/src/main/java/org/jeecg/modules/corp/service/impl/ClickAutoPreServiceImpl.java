package org.jeecg.modules.corp.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.desensitization.util.SensitiveInfoUtil;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.modules.info.service.*;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecg.config.ProvinceIpGenerator;
import org.jeecg.modules.info.util.ChinaPlateNumberGenerator;
import org.jeecg.modules.info.util.LicensePlateGenerator;
import org.jeecg.modules.corp.dto.*;
import org.jeecg.modules.corp.entity.*;
import org.jeecg.modules.corp.mapper.ClickAutoPreDao;
import org.jeecg.modules.corp.service.*;
import org.jeecg.modules.corp.util.*;
import org.jeecg.modules.demo.produce.entity.ClickReport;
import org.jeecg.modules.demo.produce.service.IClickReportService;
import org.jeecg.modules.info.dto.LedgerChatDto;
import org.jeecg.modules.info.dto.PdBatchChatDto;
import org.jeecg.modules.info.entity.PdCarInfo;
import org.jeecg.modules.info.entity.PdCasualtyInfo;
import org.jeecg.modules.info.entity.PdGuestUsers;
import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.entity.PdIntegrated;
import org.jeecg.modules.wechat.service.IPdIntegratedService;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.jeecg.modules.corp.service.IPdCarInfoRelService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.math.BigDecimal;

import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.service.ISysTenantService;
import org.jeecg.modules.wechat.entity.EaRegion;
import org.jeecg.modules.wechat.service.IEaRegionService;
import org.jeecg.modules.corp.vo.CityInfoVO;
import org.jeecg.modules.corp.service.ISysCityPlatePrefixService;
import org.jeecg.modules.consum.entity.RechargeRecode;
import org.jeecg.modules.consum.entity.RechargeRecodeDet;
import org.jeecg.modules.consum.service.IRechargeRecodeService;
import org.jeecg.modules.consum.service.IRechargeRecodeDetService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@Service
@Slf4j
public class ClickAutoPreServiceImpl extends ServiceImpl<ClickAutoPreDao, ClickAutoPre> implements IClickAutoPreService {
    @Autowired
    private ISysDeployConfigService deployConfigService;

    @Autowired
    private IClickReportService clickReportService;

    @Autowired
    private IPdGuestUsersService guestUsersService;

    @Autowired
    private ObjectMapper objectMapper;

    Random random = new Random();



    @Autowired
    private IPdInsuranceLedgerService iPdInsuranceLedgerService;

    @Autowired
    private IPdIntegratedService pdIntegratedService;

    @Autowired
    private IPdAddedLedgerService iPdAddedLedgerService;

    @Autowired
    private IPdSceneService sceneService;

    @Autowired
    private IPdLedgerService iPdLedgerService;

    @Autowired
    private IPdLinkInfoService iPdLinkInfoService;
    @Autowired
    private IPdCasualtyInfoService iPdCasualtyInfoService;
    @Autowired
    private IPdAddedService iPdAddedService;
    @Autowired
    private IPdCarInfoService iPdCarInfoService;
    @Resource
    private IPdChatSourceService chatSourceService;
    @Autowired
    private IDailyConfigService dailyConfigService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IPdCarInfoRelService pdCarInfoRelService;

    @Autowired
    private IClickReportHourlyService clickReportHourlyService;

    @Autowired
    private ISysTenantService sysTenantService;

    @Autowired
    private IEaRegionService eaRegionService;

    @Autowired
    private ISysCityPlatePrefixService sysCityPlatePrefixService;

    @Autowired
    private IPdLinkRecodeService pdLinkRecodeService;

    @Autowired
    private IRechargeRecodeService rechargeRecodeService;

    @Autowired
    private IRechargeRecodeDetService rechargeRecodeDetService;
    /**
    * 新增
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickAutoPre add(ClickAutoPre dto){
        save(dto);
        return dto;
    }

    /**
    * 修改
    *
    * @param dto 参数
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClickAutoPre edit(ClickAutoPre dto){
        updateById(dto);
        return dto;
    }

    /**
    * 删除
    *
    * @param id 主键
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(String id){
        baseMapper.deleteById(id);
    }

    /**
    * 根据id获取 详情
    *
    * @param id 主键
    */
    @Override
    public ClickAutoPre queryById(String id){
         return checkEntity(id);
    }


    /**
    * 分页查询
    *
    * @param dto 参数
    * @return
    */
    @Override
    public IPage<ClickAutoPre>  findPage(IPage<ClickAutoPre> page,ClickAutoPre dto) {
        return page;
    }

    /**
    * 根据id获取 实体
    *
    * @param id 主键
    */
    @Override
    public ClickAutoPre checkEntity(String id){
        if (StringUtils.isBlank(id)){
            throw new IllegalArgumentException("点击数预生成未查询到");
        }
        ClickAutoPre entity = baseMapper.selectById(id);

        return entity;
    }
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickPreAll() {
        log.info("--点击数预生成开始--");

        try {
            // 1. 获取所有配置
            List<DailyConfig> dailyConfigList = dailyConfigService.lambdaQuery().list();

            if (dailyConfigList.isEmpty()) {
                log.info("没有找到任何配置，跳过处理");
                return;
            }

            log.info("找到{}个配置，开始异步处理", dailyConfigList.size());

            // 2. 将配置信息存入Redis队列
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            // 清空之前的配置（如果有）
            redisTemplate.delete(redisKey);
            redisTemplate.delete(processingKey);
            redisTemplate.delete(currentConfigKey);
            redisTemplate.delete(completedConfigsKey);

            // 将每个配置转换为JSON并存入Redis
            for (DailyConfig dailyConfig : dailyConfigList) {
                // 创建配置信息对象
                Map<String, Object> configInfo = new HashMap<>();
                configInfo.put("id", dailyConfig.getId());
                configInfo.put("tenantId", dailyConfig.getTenantId());
                configInfo.put("configJson", dailyConfig.getConfigJson());
                configInfo.put("clickStart", dailyConfig.getClickStart());
                configInfo.put("clickEnd", dailyConfig.getClickEnd());

                // 获取租户名称
                String tenantName = "未知租户";
                try {
                    SysTenant tenant = sysTenantService.getById(dailyConfig.getTenantId());
                    if (tenant != null) {
                        tenantName = tenant.getName();
                    }
                } catch (Exception e) {
                    log.error("获取租户名称失败", e);
                    tenantName = "租户" + dailyConfig.getTenantId();
                }
                configInfo.put("tenantName", tenantName);

                // 计算点击数平均值
                int avgClicks = (dailyConfig.getClickStart() + dailyConfig.getClickEnd()) / 2;
                configInfo.put("avgClicks", avgClicks);

                // 存入Redis队列
                redisTemplate.opsForList().rightPush(redisKey, objectMapper.writeValueAsString(configInfo));
            }

            // 记录初始任务总数
            Long totalTasks = redisTemplate.opsForList().size(redisKey);
            redisTemplate.opsForValue().set("click_pre_batch_process:total_tasks", totalTasks.toString());

            // 设置Redis过期时间到明天结束
            Calendar tomorrow = Calendar.getInstance();
            tomorrow.add(Calendar.DAY_OF_MONTH, 1);
            tomorrow.set(Calendar.HOUR_OF_DAY, 23);
            tomorrow.set(Calendar.MINUTE, 59);
            tomorrow.set(Calendar.SECOND, 59);

            long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

            redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(currentConfigKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

            log.info("成功将{}个配置加入处理队列，总任务数={}", dailyConfigList.size(), totalTasks);

            // 3. 启动异步处理任务
            processClickPreConfigsAsync();

            log.info("点击数预生成异步任务已启动");

        } catch (Exception e) {
            log.error("点击数预生成初始化失败", e);
            throw new JeecgBootException("点击数预生成初始化失败: " + e.getMessage());
        }
    }
    /**
     * 根据配置预生成数据
     * @param dailyConfig dailyConfig
     * @throws JsonProcessingException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickPre(DailyConfig dailyConfig) throws JsonProcessingException {
        log.info("--点击数开始--" + dailyConfig.getId());
        // 将 dailyConfig 的 JSON 配置内容转换为 DailyConfigContent 对象
        DailyConfigContent configContent = objectMapper.readValue(dailyConfig.getConfigJson(), DailyConfigContent.class);

        // 获取目标日期，这里是当前日期的下一天
        LocalDate targetDate = LocalDate.now().plusDays(1);


        // 用来存储生成的 ClickAutoPre 列表
        List<ClickAutoPre> preList = new ArrayList<>();
        if (!Objects.isNull(configContent.getFinanceLedger())) {
            preList.addAll(createTypeList(1, dailyConfig, configContent,configContent.getFinanceLedger(), targetDate));
        }
        if (!Objects.isNull(configContent.getCarLedger())) {
            preList.addAll(createTypeList(3, dailyConfig, configContent,configContent.getCarLedger(), targetDate));
        }
        if (!Objects.isNull(configContent.getValueAddedLedger())) {
            preList.addAll(createTypeList(2, dailyConfig, configContent,configContent.getValueAddedLedger(), targetDate));
        }

        saveBatch(preList);
        log.info("--点击数预结束--" + preList.size());
    }

    public List<ClickAutoPre> createTypeList(Integer ledgerType,DailyConfig dailyConfig,DailyConfigContent configContent,DailyConfigContent.Range range,LocalDate targetDate){
        //格式化城市,处理一级城市编码的情况
        DailyConfigContent cityList = eaRegionService.formatCity(configContent.getCityRatios());
        Map<String, LocalDateTime> resultMap = RandDayNumUtil.generateTimeMap(targetDate, dailyConfig.getClickStart(), dailyConfig.getClickEnd());
        Map<String, LocalDateTime> ledgerTimeMap = RandDayNumUtil.pickByRandomRate( resultMap, range.getLedgerStart(), range.getLedgerEnd());
        Map<String, LocalDateTime> chatUserTimeMap = RandDayNumUtil.pickByRandomRate( ledgerTimeMap, range.getChatUserStart(), range.getChatUserEnd());

        List<ClickAutoPre> preList = new ArrayList<>();
        for (String key : resultMap.keySet()){
            ClickAutoPre autoPre = new ClickAutoPre();
            autoPre.setTenantId(dailyConfig.getTenantId());
            autoPre.setClickTime(Timestamp.valueOf(resultMap.get(key)));
            if (ledgerTimeMap.containsKey(key)){
                autoPre.setAutoCreate(1);
            }
            if (chatUserTimeMap.containsKey(key)){
                autoPre.setHasChatUser(1);
            }
            autoPre.setCity(cityList.getRandomCity());
            autoPre.setLedgerType(ledgerType);
            preList.add(autoPre);
        }
        return preList;
    }

    /**
     * 异步处理点击数预生成配置
     * 从Redis队列中逐个取出配置并处理
     */
    public void processClickPreConfigsAsync() {
        // 创建并启动一个新线程来处理队列
        Thread processThread = new Thread(() -> {
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            try {
                // 设置处理状态为正在处理
                redisTemplate.opsForValue().set(processingKey, "1");

                // 设置Redis过期时间到明天结束
                Calendar tomorrow = Calendar.getInstance();
                tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                tomorrow.set(Calendar.HOUR_OF_DAY, 23);
                tomorrow.set(Calendar.MINUTE, 59);
                tomorrow.set(Calendar.SECOND, 59);

                long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentConfigKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

                while (true) {
                    // 从队列左侧弹出一个配置（先进先出）
                    String configJson = (String) redisTemplate.opsForList().leftPop(redisKey);

                    // 如果队列为空，处理完成
                    if (configJson == null) {
                        // 清除当前处理的配置信息
                        redisTemplate.delete(currentConfigKey);
                        break;
                    }

                    try {
                        // 解析配置信息
                        Map<String, Object> configInfo = objectMapper.readValue(configJson,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 记录当前处理的配置信息
                        Map<String, Object> currentConfigInfo = new HashMap<>();
                        currentConfigInfo.put("tenantId", configInfo.get("tenantId"));
                        currentConfigInfo.put("tenantName", configInfo.get("tenantName"));
                        currentConfigInfo.put("avgClicks", configInfo.get("avgClicks"));
                        currentConfigInfo.put("startTime", new Date());
                        currentConfigInfo.put("status", "处理中");

                        redisTemplate.opsForValue().set(currentConfigKey, objectMapper.writeValueAsString(currentConfigInfo));

                        log.info("开始处理配置: 租户ID={}, 租户名称={}",
                            configInfo.get("tenantId"), configInfo.get("tenantName"));

                        // 重新构建DailyConfig对象
                        DailyConfig dailyConfig = new DailyConfig();
                        dailyConfig.setId((String) configInfo.get("id"));
                        dailyConfig.setTenantId((Integer) configInfo.get("tenantId"));
                        dailyConfig.setConfigJson((String) configInfo.get("configJson"));
                        dailyConfig.setClickStart((Integer) configInfo.get("clickStart"));
                        dailyConfig.setClickEnd((Integer) configInfo.get("clickEnd"));

                        // 处理配置
                        autoCreateClickPre(dailyConfig);

                        // 记录完成时间
                        Date endTime = new Date();

                        // 创建已完成的配置信息
                        Map<String, Object> completedConfigInfo = new HashMap<>();
                        completedConfigInfo.put("tenantId", configInfo.get("tenantId"));
                        completedConfigInfo.put("tenantName", configInfo.get("tenantName"));
                        completedConfigInfo.put("avgClicks", configInfo.get("avgClicks"));
                        completedConfigInfo.put("startTime", currentConfigInfo.get("startTime"));
                        completedConfigInfo.put("endTime", endTime);
                        completedConfigInfo.put("status", "已完成");

                        // 将已完成的配置信息添加到列表
                        redisTemplate.opsForList().rightPush(completedConfigsKey, objectMapper.writeValueAsString(completedConfigInfo));

                        log.info("配置处理完成: 租户ID={}, 租户名称={}",
                            configInfo.get("tenantId"), configInfo.get("tenantName"));

                        // 添加适当的延迟，避免系统负载过高
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("处理配置失败: {}", configJson, e);
                        // 处理失败的配置放回队列末尾，以便稍后重试
                        redisTemplate.opsForList().rightPush(redisKey, configJson);
                        // 添加延迟，避免立即重试
                        Thread.sleep(5000);
                    }
                }

                // 所有任务处理完成，设置状态为完成
                redisTemplate.opsForValue().set(processingKey, "0");
                log.info("所有点击数预生成配置处理完成");

            } catch (Exception e) {
                log.error("点击数预生成配置批处理失败", e);
                // 设置处理状态为失败
                try {
                    redisTemplate.opsForValue().set(processingKey, "-1");
                } catch (Exception ex) {
                    log.error("设置处理状态失败", ex);
                }
            }
        });

        // 设置为守护线程，不阻止JVM退出
        processThread.setDaemon(true);
        processThread.setName("ClickPreBatchProcessor");
        processThread.start();
    }

    /**
     * 每日定时生成方法
     * @throws ParseException
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClickAll() throws ParseException {
        log.info("--每日定时生成开始--");

        try {
            // 1. 获取所有配置，按租户分组
            List<DailyConfig> dailyConfigList = dailyConfigService.lambdaQuery().list();

            if (dailyConfigList.isEmpty()) {
                log.info("没有找到任何配置，跳过处理");
                return;
            }

            // 按租户ID分组
            Map<Integer, List<DailyConfig>> configsByTenant = dailyConfigList.stream()
                    .collect(Collectors.groupingBy(DailyConfig::getTenantId));

            log.debug("找到{}个租户的配置，开始异步处理", configsByTenant.size());

            // 2. 将租户配置信息存入Redis队列
            String redisKey = "click_daily_batch_process:tenants";
            String processingKey = "click_daily_batch_process:processing";
            String currentTenantKey = "click_daily_batch_process:current_tenant";
            String completedTenantsKey = "click_daily_batch_process:completed_tenants";

            // 清空之前的配置（如果有）
            redisTemplate.delete(redisKey);
            redisTemplate.delete(processingKey);
            redisTemplate.delete(currentTenantKey);
            redisTemplate.delete(completedTenantsKey);

            // 将每个租户的配置转换为JSON并存入Redis
            for (Map.Entry<Integer, List<DailyConfig>> entry : configsByTenant.entrySet()) {
                Integer tenantId = entry.getKey();
                List<DailyConfig> tenantConfigs = entry.getValue();

                // 创建租户配置信息对象
                Map<String, Object> tenantInfo = new HashMap<>();
                tenantInfo.put("tenantId", tenantId);
                tenantInfo.put("configCount", tenantConfigs.size());

                // 获取租户名称
                String tenantName = "未知租户";
                try {
                    SysTenant tenant = sysTenantService.getById(tenantId);
                    if (tenant != null) {
                        tenantName = tenant.getName();
                    }
                } catch (Exception e) {
                    log.error("获取租户名称失败", e);
                    tenantName = "租户" + tenantId;
                }
                tenantInfo.put("tenantName", tenantName);

                // 将配置列表转换为简化的配置信息
                List<Map<String, Object>> configInfoList = new ArrayList<>();
                for (DailyConfig config : tenantConfigs) {
                    Map<String, Object> configInfo = new HashMap<>();
                    configInfo.put("id", config.getId());
                    configInfo.put("tenantId", config.getTenantId());
                    configInfoList.add(configInfo);
                }
                tenantInfo.put("configs", configInfoList);

                // 存入Redis队列
                redisTemplate.opsForList().rightPush(redisKey, objectMapper.writeValueAsString(tenantInfo));
            }

            // 记录初始任务总数
            Long totalTenants = redisTemplate.opsForList().size(redisKey);
            redisTemplate.opsForValue().set("click_daily_batch_process:total_tenants", totalTenants.toString());

            // 设置Redis过期时间到明天结束
            Calendar tomorrow = Calendar.getInstance();
            tomorrow.add(Calendar.DAY_OF_MONTH, 1);
            tomorrow.set(Calendar.HOUR_OF_DAY, 23);
            tomorrow.set(Calendar.MINUTE, 59);
            tomorrow.set(Calendar.SECOND, 59);

            long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

            redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire("click_daily_batch_process:total_tenants", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(currentTenantKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
            redisTemplate.expire(completedTenantsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

            log.info("成功将{}个租户的配置加入处理队列，总租户数={}", configsByTenant.size(), totalTenants);

            // 3. 启动异步处理任务
            processClickDailyTenantsAsync();

            log.info("每日定时生成异步任务已启动");

        } catch (Exception e) {
            log.error("每日定时生成初始化失败", e);
            throw new JeecgBootException("每日定时生成初始化失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理每日定时生成租户队列
     * 从Redis队列中逐个取出租户并处理其配置
     */
    public void processClickDailyTenantsAsync() {
        // 创建并启动一个新线程来处理队列
        Thread processThread = new Thread(() -> {
            String redisKey = "click_daily_batch_process:tenants";
            String processingKey = "click_daily_batch_process:processing";
            String currentTenantKey = "click_daily_batch_process:current_tenant";
            String completedTenantsKey = "click_daily_batch_process:completed_tenants";

            try {
                // 设置处理状态为正在处理
                redisTemplate.opsForValue().set(processingKey, "1");

                // 设置Redis过期时间到明天结束
                Calendar tomorrow = Calendar.getInstance();
                tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                tomorrow.set(Calendar.HOUR_OF_DAY, 23);
                tomorrow.set(Calendar.MINUTE, 59);
                tomorrow.set(Calendar.SECOND, 59);

                long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire("click_daily_batch_process:total_tenants", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentTenantKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedTenantsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);

                while (true) {
                    // 从队列左侧弹出一个租户配置（先进先出）
                    String tenantJson = (String) redisTemplate.opsForList().leftPop(redisKey);

                    // 如果队列为空，处理完成
                    if (tenantJson == null) {
                        // 清除当前处理的租户信息
                        redisTemplate.delete(currentTenantKey);
                        break;
                    }

                    try {
                        // 解析租户信息
                        Map<String, Object> tenantInfo = objectMapper.readValue(tenantJson,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 记录当前处理的租户信息
                        Map<String, Object> currentTenantInfo = new HashMap<>();
                        currentTenantInfo.put("tenantId", tenantInfo.get("tenantId"));
                        currentTenantInfo.put("tenantName", tenantInfo.get("tenantName"));
                        currentTenantInfo.put("configCount", tenantInfo.get("configCount"));
                        currentTenantInfo.put("startTime", new Date());
                        currentTenantInfo.put("status", "处理中");

                        redisTemplate.opsForValue().set(currentTenantKey, objectMapper.writeValueAsString(currentTenantInfo));

                        log.info("开始处理租户: 租户ID={}, 租户名称={}, 配置数量={}",
                            tenantInfo.get("tenantId"), tenantInfo.get("tenantName"), tenantInfo.get("configCount"));

                        // 获取租户的配置列表
                        Integer tenantId = (Integer) tenantInfo.get("tenantId");
                        List<Map<String, Object>> configInfoList = (List<Map<String, Object>>) tenantInfo.get("configs");

                        // 处理该租户的所有配置
                        for (Map<String, Object> configInfo : configInfoList) {
                            String configId = (String) configInfo.get("id");

                            // 重新从数据库获取配置详情
                            DailyConfig dailyConfig = dailyConfigService.getById(configId);
                            if (dailyConfig != null) {
                                log.info("处理租户{}的配置: {}", tenantId, configId);
                                autoCreateClick(dailyConfig);
                            } else {
                                log.warn("配置不存在: {}", configId);
                            }
                        }

                        // 记录完成时间
                        Date endTime = new Date();

                        // 创建已完成的租户信息
                        Map<String, Object> completedTenantInfo = new HashMap<>();
                        completedTenantInfo.put("tenantId", tenantInfo.get("tenantId"));
                        completedTenantInfo.put("tenantName", tenantInfo.get("tenantName"));
                        completedTenantInfo.put("configCount", tenantInfo.get("configCount"));
                        completedTenantInfo.put("startTime", currentTenantInfo.get("startTime"));
                        completedTenantInfo.put("endTime", endTime);
                        completedTenantInfo.put("status", "已完成");

                        // 将已完成的租户信息添加到列表
                        redisTemplate.opsForList().rightPush(completedTenantsKey, objectMapper.writeValueAsString(completedTenantInfo));

                        log.info("租户处理完成: 租户ID={}, 租户名称={}",
                            tenantInfo.get("tenantId"), tenantInfo.get("tenantName"));

                        // 添加适当的延迟，避免系统负载过高
                        Thread.sleep(2000);
                    } catch (Exception e) {
                        log.error("处理租户失败: {}", tenantJson, e);
                        // 处理失败的租户放回队列末尾，以便稍后重试
                        redisTemplate.opsForList().rightPush(redisKey, tenantJson);
                        // 添加延迟，避免立即重试
                        Thread.sleep(10000);
                    }
                }

                // 所有任务处理完成，设置状态为完成
                redisTemplate.opsForValue().set(processingKey, "0");
                log.info("所有租户的每日定时生成处理完成");

            } catch (Exception e) {
                log.error("每日定时生成租户批处理失败", e);
                // 设置处理状态为失败
                try {
                    redisTemplate.opsForValue().set(processingKey, "-1");
                } catch (Exception ex) {
                    log.error("设置处理状态失败", ex);
                }
            }
        });

        // 设置为守护线程，不阻止JVM退出
        processThread.setDaemon(true);
        processThread.setName("ClickDailyBatchProcessor");
        processThread.start();
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void autoCreateClick(DailyConfig dailyConfig) throws ParseException {
        long now = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // 当天 00:00:00
        Date startDate = sdf.parse(sdf.format(new Date(now)));

        // 查询 click_time 在 [今天 00:00:00, 当前时间)，且 companyId 相等
        List<ClickAutoPre> preList = lambdaQuery()
                .eq(ClickAutoPre::getTenantId, dailyConfig.getTenantId())
                .ge(ClickAutoPre::getClickTime, new Timestamp(startDate.getTime()))
                .lt(ClickAutoPre::getClickTime, new Timestamp(now))  // 小于当前时间
                .list();

        ClickReport clickReport= clickReportService.lambdaQuery()
                .eq(ClickReport::getStatDate,startDate)
                .eq(ClickReport::getTenantId,dailyConfig.getTenantId())
                .last("limit 1")
                .one();
        if (clickReport == null){
            clickReport = new ClickReport();
            clickReport.setStatDate(startDate);
            clickReport.setClickNum(preList.size());
            clickReport.setTenantId(dailyConfig.getTenantId());
            clickReportService.save(clickReport);
        }else {
            clickReport.setClickNum(clickReport.getClickNum()+preList.size());
            clickReportService.updateById(clickReport);
        }

        List<ClickReportHourly> clickReportHourList= clickReportHourlyService.lambdaQuery()
                .eq(ClickReportHourly::getStatDate,startDate)
                .eq(ClickReportHourly::getTenantId,dailyConfig.getTenantId())
                .list();
        Map<String, ClickReportHourly> clickReportHourlyMap = CollectionUtils.isEmpty(clickReportHourList)
                ? new HashMap<>()
                : clickReportHourList.stream().collect(Collectors.toMap(
                item -> item.getStatDate().toString() + item.getHour(), // key: yyyy-MM-dd + hour
                item -> item,
                (existing, replacement) -> existing // 若有重复 key，保留原始的
        ));

        // 获取 PV/UV 比值配置
        DeployConfigDTO deployConfig = null;
        try {
            deployConfig = deployConfigService.getDeployConfig();
        } catch (Exception e) {
            log.warn("获取配置失败，将使用默认随机生成方式: {}", e.getMessage());
        }

        List<ClickReportHourly> saveList = new ArrayList<>();
        for (ClickAutoPre clickAutoPre : preList){
            Timestamp clickTime = clickAutoPre.getClickTime();
            // 格式化为 yyyy-MM-dd
            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(clickTime);
            // 获取小时（0-23）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(clickTime);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            // 构造 key
            String key = dateStr + hour;
            if (clickReportHourlyMap.containsKey(key)) {
                ClickReportHourly clickReportHourly = clickReportHourlyMap.get(key);
                int newClickNum = clickReportHourly.getClickNum() + 1;
                clickReportHourly.setClickNum(newClickNum);

                // 生成对应的 PV 值
                int generatedClickPv = generateClickPv(1, deployConfig); // 每次增加1个UV，生成对应的PV
                clickReportHourly.setClickPv((clickReportHourly.getClickPv() == null ? 0 : clickReportHourly.getClickPv()) + generatedClickPv);
            }else {
                ClickReportHourly clickReportHourly = new ClickReportHourly();

                // 将格式化字符串再转回 java.util.Date（代表当天 00:00:00）
                clickReportHourly.setStatDate(sdf.parse(dateStr));
                clickReportHourly.setHour(hour);
                clickReportHourly.setClickNum(1);

                // 生成对应的 PV 值
                int generatedClickPv = generateClickPv(1, deployConfig);
                clickReportHourly.setClickPv(generatedClickPv);

                clickReportHourly.setTenantId(dailyConfig.getTenantId());
                saveList.add(clickReportHourly);
                clickReportHourlyMap.put(key, clickReportHourly);
            }
        }
        if (!CollectionUtils.isEmpty(saveList)){
            clickReportHourlyService.saveBatch(saveList);
        }
        if (!CollectionUtils.isEmpty(clickReportHourList)){
            clickReportHourlyService.updateBatchById(clickReportHourList);
        }
        if (CollectionUtils.isEmpty(preList)){
            return;
        }
        List<ClickAutoPre> clickAutoPreList = preList.stream().filter(clickAutoPre -> clickAutoPre.getAutoCreate() == 1).collect(Collectors.toList());
        autoCreateLedger(clickAutoPreList, dailyConfig.getTenantId());
        //修改以下删除语句,改为这样删除
        // 查询 click_time 在 [今天 00:00:00, 当前时间)，且 companyId 相等
        //修改以下,按照条件删除
        lambdaUpdate()
                .eq(ClickAutoPre::getTenantId, dailyConfig.getTenantId())
                .ge(ClickAutoPre::getClickTime, new Timestamp(startDate.getTime()))
                .lt(ClickAutoPre::getClickTime, new Timestamp(now)) // 小于当前时间
                .remove();
    }
    /**
     *
     */

    /**
     * 分批处理台账生成
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     * @param batchSize 每批处理的数量
     */
    @Transactional(propagation = Propagation.NEVER) // 禁止事务传播，确保每个批次有自己的事务
    public void batchCreateLedger(List<ClickAutoPre> preList, Integer tenantId, int batchSize) {
        if (CollectionUtils.isEmpty(preList)) {
            return;
        }

        // 如果批次大小无效，设置默认值
        if (batchSize <= 0) {
            batchSize = 3000; // 默认每批3000条
        }

        // 计算总批次数
        int totalSize = preList.size();
        int batchCount = (totalSize + batchSize - 1) / batchSize;


        // 分批处理
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, totalSize);
            List<ClickAutoPre> batchList = preList.subList(fromIndex, toIndex);

            log.info("处理第 {}/{} 批，数据量：{}", i + 1, batchCount, batchList.size());

            try {
                // 在新的事务中处理每个批次
                processLedgerBatch(batchList, tenantId);
                log.info("第 {}/{} 批处理完成", i + 1, batchCount);
            } catch (Exception e) {
                log.error("第 {}/{} 批处理失败", i + 1, batchCount, e);
                // 这里可以选择继续处理下一批，或者抛出异常中断整个处理
                // throw new RuntimeException("批处理失败", e);
            }
        }

    }

    /**
     * 在新事务中处理一个批次的台账生成
     * @param batchList 批次数据
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class) // 确保每个批次在单独的事务中
    public void processLedgerBatch(List<ClickAutoPre> batchList, Integer tenantId) {
        // 调用原有的处理逻辑
        autoCreateLedgerInternal(batchList, tenantId);
    }

    /**
     * 生成台账（原方法，为了兼容性保留）
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     */
    public void autoCreateLedger(List<ClickAutoPre> preList, Integer tenantId) {
        // 调用分批处理方法，默认每批3000条数据
        batchCreateLedger(preList, tenantId, 2000);
    }

    /**
     * 生成台账（内部方法，由批处理方法调用）
     * @param preList 点击预生成列表
     * @param tenantId 租户ID
     */
    private void autoCreateLedgerInternal(List<ClickAutoPre> preList, Integer tenantId) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理批次，数据量：{}", preList.size());

        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        // 为每个车辆创建服务关系
        Random random = new Random();
        if (deployConfig == null) {
            return;
        }
        boolean configChange = false;
        if (deployConfig.getQueryType().equals("db")) {
            configChange = true;
        }
        //台账类型(1.财险台账 2.增值服务台账 3.车险台账)
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Map<Integer,List<ClickAutoPre>> mapByType = preList.stream().collect(Collectors.groupingBy(ClickAutoPre::getLedgerType));
//        List<PdInsuranceLedger> pdInsuranceLedgerList = new ArrayList<>();
//        List<PdAddedLedger> pdAddedLedgerList = new ArrayList<>();
//        List<PdLedger> pdLedgerList = new ArrayList<>();
        List<PdCasualtyInfo> pdCasualtyInfoList = new ArrayList<>();
        List<PdAdded> pdAddedList = new ArrayList<>();
        List<PdCarInfo> carInfoList = new ArrayList<>();
        List<PdCarInfoRel> carInfoRelList = new ArrayList<>(); // 新增车辆服务关系列表
        for (Integer ledgerType : mapByType.keySet()){
            List<ClickAutoPre> listByType = mapByType.get(ledgerType);
            for (ClickAutoPre clickAutoPre : listByType){
                // 根据isExcel判断处理逻辑
                boolean isExcelImport = clickAutoPre.getIsExcel() != null && clickAutoPre.getIsExcel() == 1;

                String name;
                String licensePlate;
                String maskedPhone;
                String signDate;
                String vin;
                CityInfoVO cityInfo;

                if (isExcelImport) {
                    // 表格导入：根据类型从台账实体中获取字段值，为空则生成随机值
                    cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());

                    // 根据台账类型获取对应的实体字段值
                    if (ledgerType == 1 && clickAutoPre.getPdInsuranceLedger() != null) {
                        // 财险台账
                        PdInsuranceLedger insuranceLedger = clickAutoPre.getPdInsuranceLedger();
                        name = StringUtils.isNotBlank(insuranceLedger.getName()) ? insuranceLedger.getName() : sceneService.getRandomName();
                        maskedPhone = StringUtils.isNotBlank(insuranceLedger.getPhone()) ? insuranceLedger.getPhone() : ProvinceIpGenerator.generateMaskedPhone();
                        // 财险不需要车牌号和车架号
                        licensePlate = null;
                        vin = null;

                        // 根据配置进行脱敏处理
                        name = applyDesensitization(name, "name", deployConfig);
                        maskedPhone = applyDesensitization(maskedPhone, "phone", deployConfig);
                        cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());

                        // 将处理后的值设置回实体
                        insuranceLedger.setName(name);
                        insuranceLedger.setPhone(maskedPhone);

                    } else if (ledgerType == 2 && clickAutoPre.getPdAddedLedger() != null) {
                        // 增值服务台账
                        PdAddedLedger addedLedger = clickAutoPre.getPdAddedLedger();
                        name = StringUtils.isNotBlank(addedLedger.getName()) ? addedLedger.getName() : sceneService.getRandomName();
                        maskedPhone = StringUtils.isNotBlank(addedLedger.getPhone()) ? addedLedger.getPhone() : ProvinceIpGenerator.generateMaskedPhone();
                        // 增值服务不需要车牌号和车架号
                        licensePlate = null;
                        vin = null;

                        // 根据配置进行脱敏处理
                        name = applyDesensitization(name, "name", deployConfig);
                        maskedPhone = applyDesensitization(maskedPhone, "phone", deployConfig);
                        cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());

                        // 将处理后的值设置回实体
                        addedLedger.setName(name);
                        addedLedger.setPhone(maskedPhone);

                    } else if (ledgerType == 3 && clickAutoPre.getPdLedger() != null) {
                        // 车险台账
                        PdLedger vehicleLedger = clickAutoPre.getPdLedger();
                        signDate = StringUtils.isNotBlank(vehicleLedger.getSignDate()) ? vehicleLedger.getSignDate() : sceneService.getRandomName();
                        name = StringUtils.isNotBlank(vehicleLedger.getPolicyholder()) ? vehicleLedger.getPolicyholder() : sceneService.getRandomName();
                        maskedPhone = StringUtils.isNotBlank(vehicleLedger.getPhoneNumber()) ? vehicleLedger.getPhoneNumber() : ProvinceIpGenerator.generateMaskedPhone();
                        licensePlate = StringUtils.isNotBlank(vehicleLedger.getLicensePlate()) ? vehicleLedger.getLicensePlate() :
                                      (cityInfo != null ? cityInfo.getLicensePlate() : ChinaPlateNumberGenerator.generatePlateNumber(clickAutoPre.getCity()));
                        vin = StringUtils.isNotBlank(vehicleLedger.getVin()) ? vehicleLedger.getVin() : RandomVinGenerator.generateRandomVin();

                        // 根据配置进行脱敏处理
                        name = applyDesensitization(name, "name", deployConfig);
                        maskedPhone = applyDesensitization(maskedPhone, "phone", deployConfig);
                        licensePlate = applyDesensitization(licensePlate, "licensePlate", deployConfig);
                        vin = applyDesensitization(vin, "vin", deployConfig);
                        cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());

                        // 将处理后的值设置回实体
                        vehicleLedger.setPolicyholder(name);
                        vehicleLedger.setInsured(name); // 被保人使用投保人姓名
                        vehicleLedger.setPhoneNumber(maskedPhone);
                        vehicleLedger.setLicensePlate(licensePlate);
                        vehicleLedger.setVin(vin);

                    } else {
                        // 兜底逻辑：如果没有对应的台账实体，使用系统生成
                        name = sceneService.getRandomName();
                        maskedPhone = ProvinceIpGenerator.generateMaskedPhone();
                        cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());
                        licensePlate = cityInfo != null ? cityInfo.getLicensePlate() : ChinaPlateNumberGenerator.generatePlateNumber(clickAutoPre.getCity());
                        vin = RandomVinGenerator.generateRandomVin();
                    }
                } else {
                    // 系统自动生成：按原有逻辑生成所有随机值
                    name = sceneService.getRandomName();
                    cityInfo = getCityInfoByCityCode(clickAutoPre.getCity());
                    licensePlate = cityInfo != null ? cityInfo.getLicensePlate() : ChinaPlateNumberGenerator.generatePlateNumber(clickAutoPre.getCity());
                    maskedPhone = ProvinceIpGenerator.generateMaskedPhone();
                    vin = RandomVinGenerator.generateRandomVin();
                }
                if (configChange) {
                    if (deployConfig.getNameSwitch()) {
                        // 截取名字的第一个字（姓）
                        String surname = name.substring(0, 1);
                        // 随机选择“先生”或“女士”
                        String title = random.nextBoolean() ? "先生" : "女士";  // 随机选择
                        name = surname + title;
                    }
                    if (deployConfig.getPlateNoMask() && licensePlate != null && licensePlate.length() >= 7 && clickAutoPre.getLedgerType()==3) {
                        try {
                            // 截取车牌号的前4位，例如“粤A12”
                            String prefix = licensePlate.substring(0, 4);

                            // 截取后两位，例如“45”
                            String suffix = licensePlate.substring(6);

                            // 拼接脱敏后的车牌号
                            String maskedPlate = prefix + "**" + suffix;

                            // 去除所有空白字符（空格、换行、制表符等）
                            licensePlate = maskedPlate.replaceAll("\\s+", "");
                        } catch (Exception e) {
                            // 如果格式异常，保留原始值但清除空白
                            licensePlate = licensePlate.replaceAll("\\s+", "");
                        }
                    }
                    // 如果需要进行手机号脱敏，判断条件
                    if (deployConfig.getPhoneSwitch()) {
                        // 对手机号进行脱敏处理，替换中间四位为星号
                        maskedPhone = maskedPhone.substring(0, 3) + "****" + maskedPhone.substring(7);
                    }
                    if (deployConfig.getVinNoMask() && clickAutoPre.getLedgerType()==3) {
                        // 获取车架号的前8位（例如：1HGCM826）
                        String prefix = vin.substring(0, 8);  // 第0到第7位

                        // 获取车架号的后4位（例如：A123456）
                        String suffix = vin.substring(12);  // 从第12位到最后

                        // 中间四位替换为星号
                        String maskedVin = prefix + "****" + suffix;

                        // 返回脱敏后的车架号
                        vin = maskedVin;
                    }
                }
                //1.财险台账
                if (ledgerType == 1){
                    PdInsuranceLedger pdInsuranceLedger;

                    if (isExcelImport && clickAutoPre.getPdInsuranceLedger() != null) {
                        // 表格导入：使用已经处理过字段值的台账实体
                        pdInsuranceLedger = clickAutoPre.getPdInsuranceLedger();

                        // 为其他空字段填充默认值
                        if (StringUtils.isBlank(pdInsuranceLedger.getUserItem())) {
                            String randomServiceId = pdIntegratedService.getRandomServiceId(1);
                            pdInsuranceLedger.setUserItem(randomServiceId);
                        }
                        if (pdInsuranceLedger.getIsVied() == null) {
                            pdInsuranceLedger.setIsVied(0);
                        }
                    } else {
                        // 系统自动生成：创建新的台账实体
                        pdInsuranceLedger = new PdInsuranceLedger();
                        pdInsuranceLedger.setOrderDate(new java.sql.Date(clickAutoPre.getClickTime().getTime()));
                        pdInsuranceLedger.setName(name);
                        pdInsuranceLedger.setPhone(maskedPhone);
                        //服务
                        String randomServiceId = pdIntegratedService.getRandomServiceId(1);
                        pdInsuranceLedger.setUserItem(randomServiceId);
                        pdInsuranceLedger.setTenantId(clickAutoPre.getTenantId());
                        pdInsuranceLedger.setHasChatUser(clickAutoPre.getHasChatUser());
                    }

                    // 保存台账
                    iPdInsuranceLedgerService.save(pdInsuranceLedger);

                    // 生成游客用户
                    PdGuestUsers guestUsers = new PdGuestUsers();
                    if (ObjectUtil.isNull(clickAutoPre.getHasChatUser()) || clickAutoPre.getHasChatUser()!=1) {
                        //不存在用户,则需要生成游客名称
                        guestUsers = new PdGuestUsers();
                        String guestName = guestUsersService.createGuestName();
                        guestUsers.setName(guestName);
                        guestUsersService.save(guestUsers);
                    }

                    pdCasualtyInfoList.add(createPdCasualtyInfo( pdInsuranceLedger, cityInfo,guestUsers));
                }
                //2.增值服务台账
                if (ledgerType == 2){
                    PdAddedLedger pdAddedLedger;

                    if (isExcelImport && clickAutoPre.getPdAddedLedger() != null) {
                        // 表格导入：使用已经处理过字段值的台账实体
                        pdAddedLedger = clickAutoPre.getPdAddedLedger();

                        // 为其他空字段填充默认值
                        if (StringUtils.isBlank(pdAddedLedger.getUserItem())) {
                            String randomServiceId = pdIntegratedService.getRandomServiceId(2);
                            pdAddedLedger.setUserItem(randomServiceId);
                        }
                        if (pdAddedLedger.getIsVied() == null) {
                            pdAddedLedger.setIsVied(0);
                        }
                    } else {
                        // 系统自动生成：创建新的台账实体
                        pdAddedLedger = new PdAddedLedger();
                        pdAddedLedger.setOrderDate(new java.sql.Date(clickAutoPre.getClickTime().getTime()));
                        pdAddedLedger.setName(name);
                        pdAddedLedger.setPhone(maskedPhone);
                        //服务
                        String randomServiceId = pdIntegratedService.getRandomServiceId(2);
                        pdAddedLedger.setUserItem(randomServiceId);
                        pdAddedLedger.setTenantId(clickAutoPre.getTenantId());
                        pdAddedLedger.setHasChatUser(clickAutoPre.getHasChatUser());
                    }

                    // 保存台账
                    iPdAddedLedgerService.save(pdAddedLedger);

                    // 生成游客用户
                    PdGuestUsers guestUsers = new PdGuestUsers();
                    if (ObjectUtil.isNull(clickAutoPre.getHasChatUser()) || clickAutoPre.getHasChatUser()!=1) {
                        //不存在用户,则需要生成游客名称
                        guestUsers = new PdGuestUsers();
                        String guestName = guestUsersService.createGuestName();
                        guestUsers.setName(guestName);
                        guestUsersService.save(guestUsers);
                    }

                    pdAddedList.add(createPdAdded( pdAddedLedger,cityInfo,guestUsers));
                }
                //3.车险台账
                if (ledgerType == 3){
                    PdLedger pdLedger;

                    if (isExcelImport && clickAutoPre.getPdLedger() != null) {
                        // 表格导入：使用已经处理过字段值的台账实体
                        pdLedger = clickAutoPre.getPdLedger();

                        // 为其他空字段填充默认值
                        if (StringUtils.isBlank(pdLedger.getInsuranceName())) {
                            pdLedger.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
                        }
                        if (StringUtils.isBlank(pdLedger.getBrandModel())) {
                            pdLedger.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
                        }
                        if (StringUtils.isBlank(pdLedger.getIsDelete())) {
                            pdLedger.setIsDelete("0");
                        }
                        if (pdLedger.getChatStatus() == null) {
                            pdLedger.setChatStatus(0);
                        }
                        // 使用城市信息VO中的数据
                        if (cityInfo != null) {
                            if (StringUtils.isBlank(pdLedger.getCity())) {
                                pdLedger.setCity(cityInfo.getFullCityName());
                            }
                            if (StringUtils.isBlank(pdLedger.getIpAddress())) {
                                pdLedger.setIpAddress(cityInfo.getIpAddress());
                            }
                        }
                    } else {
                        // 系统自动生成：创建新的台账实体
                        pdLedger = new PdLedger();
                        pdLedger.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
                        pdLedger.setPhoneNumber(maskedPhone);
                        pdLedger.setSignDate(dateFormat.format(clickAutoPre.getClickTime()));
                        pdLedger.setSignDateTime(clickAutoPre.getClickTime());
                        pdLedger.setPolicyholder(name);
                        pdLedger.setInsured(name);
                        pdLedger.setLicensePlate(licensePlate);
                        pdLedger.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
                        pdLedger.setVin(vin);
                        pdLedger.setIsDelete("0");
                        pdLedger.setChatStatus(2);
                        pdLedger.setTenantId(clickAutoPre.getTenantId());
                        pdLedger.setHasChatUser(clickAutoPre.getHasChatUser());

                        // 使用城市信息VO中的数据
                        if (cityInfo != null) {
                            pdLedger.setCity(cityInfo.getFullCityName());
                            pdLedger.setIpAddress(cityInfo.getIpAddress());
                        }
                    }

                    // 保存台账
                    iPdLedgerService.save(pdLedger);

                    // 生成游客用户
                    PdGuestUsers guestUsers = new PdGuestUsers();
                    if (ObjectUtil.isNull(clickAutoPre.getHasChatUser()) || clickAutoPre.getHasChatUser()!=1) {
                        //不存在用户,则需要生成游客名称
                        guestUsers = new PdGuestUsers();
                        String guestName = guestUsersService.createGuestName();
                        guestUsers.setName(guestName);
                        guestUsersService.save(guestUsers);
                    }

                    // 创建车辆信息
                    PdCarInfo carInfo = createCarInfo(pdLedger, random, guestUsers, cityInfo);
                    if (carInfo != null) {
                        carInfoList.add(carInfo);
                    }

                }
            }
        }
//        if (!CollectionUtils.isEmpty(pdInsuranceLedgerList)){
//            iPdInsuranceLedgerService.saveBatch(pdInsuranceLedgerList);
//        }
//        if (!CollectionUtils.isEmpty(pdAddedLedgerList)){
//            iPdAddedLedgerService.saveBatch(pdAddedLedgerList);
//        }
//        if (!CollectionUtils.isEmpty(pdLedgerList)){
//            iPdLedgerService.saveBatch(pdLedgerList);
//        }

        if (!CollectionUtils.isEmpty(pdCasualtyInfoList)){

            iPdCasualtyInfoService.saveBatch(pdCasualtyInfoList);
            updateChatCasualty(tenantId, pdCasualtyInfoList);
        }
        if (!CollectionUtils.isEmpty(pdAddedList)){
            iPdAddedService.saveBatch(pdAddedList);
            updateChatPdAdded( tenantId, pdAddedList);
        }
        if (!CollectionUtils.isEmpty(carInfoList)){
            // 批量保存车辆信息
            log.debug("批量保存车辆信息开始...");
            iPdCarInfoService.saveBatch(carInfoList);
            log.debug("批量保存车辆信息结束...");

            // 获取所有可用的服务列表（只查询一次数据库）
            List<PdIntegrated> integratedList = pdIntegratedService.list(
                new LambdaQueryWrapper<PdIntegrated>()
                    .eq(PdIntegrated::getType, 0) // 假设类型3是车辆服务
            );


            for (PdCarInfo carInfo : carInfoList) {
                // 为每辆车创建3-5个随机关系
                int relCount = random.nextInt(3) + 3;

                // 创建一个打乱的服务列表副本，以获取随机服务
                List<PdIntegrated> shuffledList = new ArrayList<>(integratedList);
                Collections.shuffle(shuffledList, random);

                // 为每辆车创建指定数量的服务关系
                for (int j = 0; j < Math.min(relCount, shuffledList.size()); j++) {
                    PdIntegrated integrated = shuffledList.get(j);
                    PdCarInfoRel pdCarInfoRel = new PdCarInfoRel();

                    // 根据input_type决定是否设置金额
                    if ("Y".equals(integrated.getInputType())) {
                        // 如果开启了输入，则设置随机金额
                        pdCarInfoRel.setAmount(random.nextInt(8) + 3);
                    } else {
                        // 如果没有开启输入，则不设置金额
                        pdCarInfoRel.setAmount(null);
                    }

                    pdCarInfoRel.setCarInfoId(carInfo.getId())
                            .setMercialId(integrated.getId())
                            .setUserId(carInfo.getGuestId());
                    carInfoRelList.add(pdCarInfoRel);
                }
            }

            // 批量保存所有服务关系
            if (!carInfoRelList.isEmpty()) {
                log.debug("批量保存车辆服务关系开始...");
                pdCarInfoRelService.saveBatch(carInfoRelList);
                log.debug("批量保存车辆服务关系完成...");
            }

            updateChatCarInfo(tenantId, carInfoList);
        }

        long endTime = System.currentTimeMillis();
        log.info("批次处理完成，数据量：{}，耗时：{}ms", preList.size(), (endTime - startTime));
    }

    public void updateChatCasualty(Integer tenantId,List<PdCasualtyInfo> pdCasualtyInfoList){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        pdCasualtyInfoList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setIpAddress(entity.getIpAddress());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDto.setLedgerType(1);
                chatDtoList.add(chatDto);
            }

        });
        pdBatchChatDto.setPidList(chatDtoList);
        pdBatchChatDto.setLedgerType(1);
        chatSourceService.createChatUser(pdBatchChatDto);
    }

    public void updateChatPdAdded(Integer tenantId,List<PdAdded> pdAddedList){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        //查询租户 id,赋值 ip地址
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        pdAddedList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setIpAddress(entity.getIpAddress());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDto.setLedgerType(2);
                chatDtoList.add(chatDto);
            }

        });
        pdBatchChatDto.setPidList(chatDtoList);
        pdBatchChatDto.setLedgerType(2);
        chatSourceService.createChatUser(pdBatchChatDto);
    }

    public void updateChatCarInfo(Integer tenantId,List<PdCarInfo> carInfoList ){
        PdBatchChatDto pdBatchChatDto = new PdBatchChatDto();
        DeployConfigDTO deployConfig = deployConfigService.getDeployConfig();
        if (deployConfig == null) {
            return;
        }
        String serviceIpType = deployConfig.getServiceIpType();
        String ipAddress = "";
        if (serviceIpType.equals("1")){
            ipAddress = chatSourceService.getIpAddressByTenantId(tenantId);
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = deployConfig.getServerIp();
            }
        }else {
            //取服务器 ip
            ipAddress = deployConfig.getServerIp();
            if (StrUtil.isEmpty(ipAddress)) {
                ipAddress = "**************";
            }
        }
        pdBatchChatDto.setIpAddress(ipAddress);
        pdBatchChatDto.setTenantId(tenantId);
        List<LedgerChatDto> chatDtoList = new ArrayList<>();
        carInfoList.forEach(entity -> {
            if (Objects.equals(entity.getHasChatUser(),1)){
                LedgerChatDto chatDto = new LedgerChatDto();
                chatDto.setId(entity.getId());
                chatDto.setIpAddress(entity.getIpAddress());
                chatDto.setCreateTime(entity.getCreateTime());
                chatDto.setLedgerType(0);
                chatDtoList.add(chatDto);
            }

        });

        pdBatchChatDto.setPidList(chatDtoList);
        pdBatchChatDto.setLedgerType(0);
        chatSourceService.createChatUser(pdBatchChatDto);
        // 更新车辆信息
        //iPdCarInfoService.updateByPid(chatDtoList.stream().map(LedgerChatDto::getId).collect(Collectors.toList()));
    }


    //财险预约信息
    public PdCasualtyInfo createPdCasualtyInfo(PdInsuranceLedger pdInsuranceLedger, CityInfoVO cityInfo, PdGuestUsers guestUsers) {
        PdCasualtyInfo pdCasualtyInfo = new PdCasualtyInfo();
        pdCasualtyInfo.setLinkType(1); // 0-车险，1-财险，2-增值服务

        // 使用传入的城市信息
        if (cityInfo != null) {
            pdCasualtyInfo.setIpAddress(cityInfo.getIpAddress()); // IP地址
            pdCasualtyInfo.setCity(cityInfo.getFullCityName());
            pdCasualtyInfo.setCityCode(cityInfo.getCityCode()); // 城市编码
            pdCasualtyInfo.setCityCodeLevel(cityInfo.getCityCodeLevel()); // 二级城市编码
        }
        if (guestUsers != null) {
            pdCasualtyInfo.setGuestName(guestUsers.getName());
        }

        String insured = pdInsuranceLedger.getName();
        String sex = random.nextBoolean() ? "男" : "女";
        if (insured.contains("女士")) {
            sex ="女";
        } else if (insured.contains("先生")) {
            sex ="男";
        }

        pdCasualtyInfo.setSource(random.nextInt(2)); // 用户来源，具体值视业务而定
        pdCasualtyInfo.setName(pdInsuranceLedger.getName()); // 姓名
        pdCasualtyInfo.setPhone(pdInsuranceLedger.getPhone()); // 手机号
        pdCasualtyInfo.setLinkId(null); // 链接ID
        pdCasualtyInfo.setSex(sex);
        //todo
        pdCasualtyInfo.setServe(pdInsuranceLedger.getUserItem()); // 选择服务
        pdCasualtyInfo.setTenantId(pdInsuranceLedger.getTenantId()); // 多租户ID
        pdCasualtyInfo.setIsVied(1); // 是否已生成：0 未生成，1 已生成
        pdCasualtyInfo.setIsDelete(0); // 是否删除：0 正常，1 删除
        pdCasualtyInfo.setCreateTime(generateRandomCreateTime(pdInsuranceLedger.getOrderDate()));
        pdCasualtyInfo.setHasChatUser(pdInsuranceLedger.getHasChatUser());
        return pdCasualtyInfo;
    }
    //增值服务预约记录
    public PdAdded createPdAdded(PdAddedLedger pdAddedLedger, CityInfoVO cityInfo, PdGuestUsers guestUsers) {

        PdAdded pdAdded = new PdAdded();

        pdAdded.setLinkType(2); // 链接类型：0-车险、1-财险、2-增值等

        String insured = pdAddedLedger.getName();
        String sex = random.nextBoolean() ? "男" : "女";
        if (insured.contains("女士")) {
            sex ="女";
        } else if (insured.contains("先生")) {
            sex ="男";
        }

        // 设置城市信息
        if (cityInfo != null) {
            pdAdded.setIpAddress(cityInfo.getIpAddress()); // IP地址
            pdAdded.setCityCode(cityInfo.getCityCode()); // 城市编码
            pdAdded.setCity(cityInfo.getFullCityName());
            pdAdded.setCityCodeLevel(cityInfo.getCityCodeLevel()); // 二级城市编码
        }
        if  (guestUsers != null) {
            pdAdded.setGuestName(guestUsers.getName()); // 游客名称
        }

        pdAdded.setSource(random.nextInt(2)); // 用户来源（自定义）
        pdAdded.setName(pdAddedLedger.getName()); // 姓名
        pdAdded.setSex(sex);
        pdAdded.setPhone(pdAddedLedger.getPhone()); // 手机号
        pdAdded.setLinkId(null); // 链接ID
        //todo
        pdAdded.setServe(pdAddedLedger.getUserItem()); // 服务名
        pdAdded.setIsVied(1); // 是否已生成：0 否，1 是
        pdAdded.setTenantId(pdAddedLedger.getTenantId()); // 多租户 ID
        pdAdded.setCreateTime(generateRandomCreateTime(pdAddedLedger.getOrderDate()));
        pdAdded.setHasChatUser(pdAddedLedger.getHasChatUser());
        return pdAdded;
    }
    public PdCarInfo createCarInfo(PdLedger pdLedger, Random random, PdGuestUsers guestUser, CityInfoVO cityInfo) {
        //根据姓名判断是否为男女,如果没有带女士,则随机姓名即可
        String insured = pdLedger.getInsured();
        String sex = random.nextBoolean() ? "男" : "女";
        if (insured.contains("女士")) {
            sex ="女";
        } else if (insured.contains("先生")) {
            sex ="男";
        }
        String licensePlate = pdLedger.getLicensePlate();

//        String address = LicensePlateToProvince.getProvinceFromLicensePlate(licensePlate.substring(0, 1));
//        String city = LicensePlateToProvince.getRandomCity(address);
//        String ipForProvince = ProvinceIpGenerator.getRandomIpForProvince(address);
        //Date date = generateRandomCreateTime(pdLedger.getSignDateTime());

        PdCarInfo carInfo = new PdCarInfo()
                .setCity(pdLedger.getCity())
                .setModel(pdLedger.getBrandModel())
                .setLicensePlateNumber(licensePlate)
                .setVinCode(pdLedger.getVin())
                .setOwner(pdLedger.getInsured())
                .setIpAddress(pdLedger.getIpAddress())
                .setPhoneNumber(pdLedger.getPhoneNumber())
                .setLedgerId(pdLedger.getId())
                .setCreateTime(pdLedger.getSignDateTime())
                .setSex(sex)
                .setIsVied(2)
                .setTenantId(pdLedger.getTenantId())
                .setHasChatUser(pdLedger.getHasChatUser())
                .setGuestId(guestUser.getId())
                .setGuestName(guestUser.getName());

        // 设置城市编码
        if (cityInfo != null) {
            carInfo.setCityCode(cityInfo.getCityCode());
            carInfo.setCityCodeLevel(cityInfo.getCityCodeLevel());  // 设置二级城市编码
        }

        return carInfo;
    }
    private Date generateRandomCreateTime(Date signDateTime) {
        Random random = new Random();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(signDateTime); // 基于 signDateTime 的日期

        // 5% 概率落在 0-6 点，其余 95% 落在 6-24 点
        int hour;
        if (random.nextDouble() < 0.05) {
            hour = random.nextInt(6); // 0-5 点
        } else {
            hour = 6 + random.nextInt(18); // 6-23 点（23:59:59 仍有效）
        }

        int minute = random.nextInt(60); // 0-59 分
        int second = random.nextInt(60); // 0-59 秒

        // 设置随机时间
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒清零，避免额外误差

        return calendar.getTime();
    }

    /**
     * 根据省份名称获取车牌前缀
     *
     * @param provinceName 省份名称
     * @return 车牌前缀，如"京A"、"沪B"等
     */
    private String getPlatePrefix(String provinceName) {
        if (StringUtils.isBlank(provinceName)) {
            return null;
        }

        // 省份简称映射
        Map<String, String> provinceMap = new HashMap<>();
        provinceMap.put("北京", "京");
        provinceMap.put("天津", "津");
        provinceMap.put("上海", "沪");
        provinceMap.put("重庆", "渝");
        provinceMap.put("河北", "冀");
        provinceMap.put("山西", "晋");
        provinceMap.put("辽宁", "辽");
        provinceMap.put("吉林", "吉");
        provinceMap.put("黑龙江", "黑");
        provinceMap.put("江苏", "苏");
        provinceMap.put("浙江", "浙");
        provinceMap.put("安徽", "皖");
        provinceMap.put("福建", "闽");
        provinceMap.put("江西", "赣");
        provinceMap.put("山东", "鲁");
        provinceMap.put("河南", "豫");
        provinceMap.put("湖北", "鄂");
        provinceMap.put("湖南", "湘");
        provinceMap.put("广东", "粤");
        provinceMap.put("海南", "琼");
        provinceMap.put("四川", "川");
        provinceMap.put("贵州", "贵");
        provinceMap.put("云南", "云");
        provinceMap.put("西藏", "藏");
        provinceMap.put("陕西", "陕");
        provinceMap.put("甘肃", "甘");
        provinceMap.put("青海", "青");
        provinceMap.put("内蒙古", "蒙");
        provinceMap.put("广西", "桂");
        provinceMap.put("宁夏", "宁");
        provinceMap.put("新疆", "新");
        provinceMap.put("香港", "港");
        provinceMap.put("澳门", "澳");
        provinceMap.put("台湾", "台");

        // 遍历省份映射，查找匹配的省份简称
        for (Map.Entry<String, String> entry : provinceMap.entrySet()) {
            if (provinceName.contains(entry.getKey())) {
                // 找到匹配的省份，返回省份简称 + "A"（默认使用A作为发牌机关代号）
                return entry.getValue() + "A";
            }
        }

        // 如果没有找到匹配的省份，返回null
        return null;
    }

    // Redis缓存前缀
    private static final String CITY_INFO_CACHE_PREFIX = "city_info:";

    /**
     * 加载单个城市信息
     *
     * @param region 城市区域信息
     * @return 城市信息VO
     */
    private CityInfoVO loadCityInfo(EaRegion region) {
        if (region == null || StringUtils.isBlank(region.getCode())) {
            return null;
        }

        String cityCode = region.getCode();
        // 获取一级和二级城市编码
        String cityCodeLevel = buildCityCodeLevel(region);

        try {
            // 获取完整城市名称和顶级城市名称
            String fullCityName = null;
            String topCityName = null;

            try {
                fullCityName = eaRegionService.getFullCityNameById(region.getId());
                topCityName = eaRegionService.getTopCityNameById(region.getId());
            } catch (Exception e) {
                log.warn("获取城市路径名称失败: {}", e.getMessage());
            }

            if (StringUtils.isBlank(fullCityName)) {
                fullCityName = region.getName();
            }
            if (StringUtils.isNotEmpty(topCityName) && StrUtil.isNotEmpty(fullCityName)) {
                fullCityName = topCityName + fullCityName;
            }

            // 获取车牌前缀
            String platePrefix = sysCityPlatePrefixService.getPlatePrefixByCityCode(cityCode);

            // 生成车牌号
            String licensePlate = null;
            if (StringUtils.isNotBlank(platePrefix)) {
                // 使用车牌前缀生成完整车牌号
                licensePlate = LicensePlateGenerator.generateRandomPlate(platePrefix);
            } else {
                // 如果没有找到车牌前缀，使用城市名称生成车牌号
                String provinceName = region.getName();
                if (StringUtils.isNotBlank(topCityName)) {
                    provinceName = topCityName;
                }
                // 尝试从省份名称中提取车牌前缀
                String prefix = getPlatePrefix(provinceName);
                if (StringUtils.isNotBlank(prefix)) {
                    licensePlate = LicensePlateGenerator.generateRandomPlate(prefix);
                } else {
                    // 如果无法提取车牌前缀，使用旧的方法生成车牌号
                    licensePlate = ChinaPlateNumberGenerator.generatePlateNumber(region.getName());
                }
            }

            // 生成IP地址
            String ipAddress = null;
            try {
                // 直接使用城市编码获取IP地址
                ipAddress = ProvinceIpGenerator.getIpByCityCode(cityCode);
            } catch (Exception e) {
                // 如果通过城市编码获取IP失败，尝试使用省份名称获取
                if (StringUtils.isNotBlank(topCityName)) {
                    ipAddress = ProvinceIpGenerator.getRandomIpForProvince(topCityName);
                }
                log.warn("通过城市编码获取IP地址失败: {}, 使用省份名称获取: {}", e.getMessage(), ipAddress);
            }

            // 创建城市信息VO
            return new CityInfoVO()
                    .setCityCode(cityCode)
                    .setCityCodeLevel(cityCodeLevel)  // 设置二级城市编码
                    .setFullCityName(fullCityName)
                    .setTopCityName(topCityName)
                    .setLicensePlate(licensePlate)
                    .setIpAddress(ipAddress);

        } catch (Exception e) {
            log.error("加载城市信息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建城市编码层级信息（一级和二级城市编码）
     * @param region 城市区域信息
     * @return 城市编码层级信息，格式：一级编码,二级编码
     */
    private String buildCityCodeLevel(EaRegion region) {
        if (region == null || region.getId() == null) {
            return "";
        }
        String cityCodes = "";

        try {

            // 获取一级城市编码（省份编码）
            String firstLevelCode = eaRegionService.getFirstLevelCityCodeById(region.getId());
            // 获取二级城市编码（地级市编码）
            String secondLevelCode = eaRegionService.getSecondLevelCityCodeById(region.getId());


            if (StringUtils.isNotBlank(firstLevelCode)) {
                cityCodes = firstLevelCode;
            }

            if (StringUtils.isNotBlank(secondLevelCode) && StringUtils.isBlank(firstLevelCode)) {
                cityCodes = secondLevelCode;
            }

            // 如果没有获取到一级和二级编码，则使用当前城市编码
            if (StringUtils.isBlank(secondLevelCode) && StringUtils.isBlank(firstLevelCode)) {
                cityCodes = region.getCode();
            }

            return cityCodes;

        } catch (Exception e) {
            log.warn("构建城市编码层级信息失败，城市: {}, 错误: {}", region.getName(), e.getMessage());
            // 发生异常时，返回当前城市编码
            return region.getCode();
        }
    }

    /**
     * 根据城市编码获取城市信息
     * 优化版本：使用Redis缓存避免重复查询相同城市信息
     *
     * @param cityCode 城市编码
     * @return 城市信息VO
     */
    public CityInfoVO getCityInfoByCityCode(String cityCode) {
        if (StringUtils.isBlank(cityCode)) {
            return null;
        }

        String cacheKey = CITY_INFO_CACHE_PREFIX + cityCode;
        CityInfoVO cityInfoVO = null;
        // 先从Redis缓存中查找
        try {
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);
            if (cachedValue != null) {
                cityInfoVO = ((CityInfoVO) cachedValue);
                cityInfoVO = cloneAndRefreshCityInfo(cityInfoVO);
                return cityInfoVO;
            }
        } catch (Exception e) {
            log.warn("从Redis获取城市信息失败: {}", e.getMessage());
            // 继续执行，从数据库查询
        }

        // 如果缓存中没有，则查询并加载到缓存
        try {
            EaRegion region = eaRegionService.findByCode(cityCode);
            if (region == null) {
                log.warn("未找到城市编码对应的城市信息: {}", cityCode);
                return null;
            }

            // 加载城市信息
             cityInfoVO = loadCityInfo(region);

            // 将结果存入Redis缓存，设置过期时间为1天（城市信息相对稳定）
            if (cityInfoVO != null) {
                try {
                    redisTemplate.opsForValue().set(cacheKey, cityInfoVO, 24, TimeUnit.HOURS);
                } catch (Exception e) {
                    log.warn("将城市信息存入Redis失败: {}", e.getMessage());
                    // 继续执行，返回查询结果
                }
            }

            return cityInfoVO;
        } catch (Exception e) {
            log.error("获取城市信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    /**
     * 克隆原始城市信息并生成新的 licensePlate 和 ipAddress
     */
    private CityInfoVO cloneAndRefreshCityInfo(CityInfoVO original) {
        CityInfoVO newCityInfoVO = new CityInfoVO();
//        if (!original.getFullCityName().equals(original.getTopCityName()) && StrUtil.isNotEmpty(original.getTopCityName())) {
//            newCityInfoVO.setFullCityName(original.getTopCityName() + original.getFullCityName());
//        }else {
//            newCityInfoVO.setFullCityName(original.getFullCityName());
//        }
        return newCityInfoVO
                .setCityCode(original.getCityCode())
                .setCityCodeLevel(original.getCityCodeLevel())  // 保留二级城市编码
                .setFullCityName(original.getFullCityName())
                .setTopCityName(original.getTopCityName())
                .setLicensePlate(LicensePlateGenerator.generateRandomPlate(original.getLicensePlate().substring(0, 2))) // 自定义生成逻辑
                .setIpAddress(ProvinceIpGenerator.getIpByCityCode(original.getCityCode()));     // 自定义生成逻辑
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importLedgers(List<MultipartFile> files,DailyConfigDto dto) throws JsonProcessingException {
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 清除点击报表数据
        clickReportService.lambdaUpdate()
                .between(ClickReport::getStatDate, monthDataStartDate, monthDataEndDate)
                .eq(ClickReport::getTenantId,dto.getTenantId())
                .remove();
        clickReportHourlyService.lambdaUpdate()
                .between(ClickReportHourly::getStatDate, monthDataStartDate, monthDataEndDate)
                .eq(ClickReportHourly::getTenantId,dto.getTenantId())
                .remove();

        // 清除财险和增值服务台账数据
        iPdInsuranceLedgerService.lambdaUpdate()
                .between(PdInsuranceLedger::getOrderDate, monthDataStartDate, monthDataEndDate)
                .eq(PdInsuranceLedger::getTenantId,dto.getTenantId())
                .remove();
        iPdAddedLedgerService.lambdaUpdate()
                .between(PdAddedLedger::getOrderDate, monthDataStartDate, monthDataEndDate)
                .eq(PdAddedLedger::getTenantId,dto.getTenantId())
                .remove();

        // 使用 deleteByTypeAndDateRange 方法删除车险台账及相关数据
        DeleteByTypeDTO deleteDto = new DeleteByTypeDTO();
        deleteDto.setType(0); // 车险类型
        deleteDto.setTenantIds(Collections.singletonList(dto.getTenantId().toString())); // 单个租户ID转为列表
        deleteDto.setStartDate(dto.getMonthDataStart());
        deleteDto.setEndDate(dto.getMonthDataEnd());

        // 调用服务方法执行删除
        iPdLedgerService.deleteByTypeAndDateRange(deleteDto);

        // 清除其他相关数据（如果 deleteByTypeAndDateRange 方法没有处理）
        LocalDateTime startOfDay = dto.getMonthDataStart().atStartOfDay();  // 转为当天 0 点（LocalDateTime）
        Date startDate = java.sql.Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
        LocalDateTime endOfDay = dto.getMonthDataEnd().atTime(23, 59, 59);  // 转为当天 23:59:59（LocalDateTime）
        Date endDate = java.sql.Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
        iPdCasualtyInfoService.lambdaUpdate()
                .between(PdCasualtyInfo::getCreateTime, startDate, endDate)
                .eq(PdCasualtyInfo::getTenantId,dto.getTenantId())
                .remove();
        iPdAddedService.lambdaUpdate()
                .between(PdAdded::getCreateTime, startDate, endDate)
                .eq(PdAdded::getTenantId,dto.getTenantId())
                .remove();
        // 先删除指定租户和日期范围内的链接快照数据
        pdLinkRecodeService.deleteByTenantAndDateRange(dto.getTenantId(), startDate, endDate);

        // 调用链接指标快照补全方法
        pdLinkRecodeService.batchCompleteRecode(dto.getTenantId(), startDate, endDate);

        String configJson = dto.getConfigJson();
        DailyConfigContent configContent = objectMapper.readValue(configJson, DailyConfigContent.class);
        //模拟点击生成
        Map<String, LocalDateTime> resultMap = RandDayRangUtil.generateRandomTimes(dto.getMonthDataStart(), dto.getMonthDataEnd(), dto.getClickNum(), dto.getClickNum());

        // 完成日消耗和充值记录的保存
        // 1. 保存充值记录
        saveRechargeRecords(configContent, dto.getTenantId());

        // 2. 计算总充值金额
        BigDecimal totalRechargeAmount = calculateTotalRechargeAmount(configContent);

        // 3. 调用独立的日消耗生成方法
        List<ClickAutoPre> clickConsumeList = generateDailyConsumption(
                resultMap, totalRechargeAmount, configContent, dto.getTenantId());

        // 5. 验证总金额和单价区间
        BigDecimal actualTotal = clickConsumeList.stream()
                .map(ClickAutoPre::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal difference = totalRechargeAmount.subtract(actualTotal);
        log.info("单价分配完成 - 目标总额: {}, 实际总额: {}, 差额: {}",
                totalRechargeAmount, actualTotal, difference);

        // 验证单价区间
        boolean allPricesInRange = true;
        for (LocalDate date : sortedDates) {
            BigDecimal unitPrice = dailyUnitPriceMap.get(date);
            if (unitPrice.compareTo(minPrice) < 0 || unitPrice.compareTo(maxPrice) > 0) {
                log.error("❌ 日期{}的单价{}超出区间[{}, {}]", date, unitPrice, minPrice, maxPrice);
                allPricesInRange = false;
            } else {
                log.info("✅ 日期{}的单价{}在区间[{}, {}]内", date, unitPrice, minPrice, maxPrice);
            }
        }

        if (allPricesInRange) {
            log.info("✅ 所有单价都在指定区间内");
        } else {
            log.error("❌ 存在单价超出区间的情况");
        }

        // 如果差额较大，进行微调
        if (difference.abs().compareTo(BigDecimal.valueOf(0.1)) > 0) {
            log.warn("差额较大({}), 需要进行微调", difference);
            // 可以在这里添加微调逻辑，但通常约束优化算法应该已经很接近了
        }

        // 6. 保存日消耗明细记录
        saveRechargeRecodeDetWithStats(clickConsumeList, totalRechargeAmount, dto.getTenantId());

        log.info("日消耗和充值记录保存完成 - 点击数: {}, 总金额: {}", resultMap.size(), totalRechargeAmount);
        Map<String, LocalDateTime> financeLedgerMap = Map.of();
        Map<String, LocalDateTime> chatUserTimeMapFinance= Map.of();
        Map<String, LocalDateTime> carLedgerMap= Map.of();

        Map<String, LocalDateTime> chatUserTimeMapCar= Map.of();
        Map<String, LocalDateTime> valueAddedLedgerMap= Map.of();
        Map<String, LocalDateTime> chatUserTimeMapValue= Map.of();
        if (!Objects.isNull( configContent.getFinanceLedger())) {
            //财险
            financeLedgerMap = RandDayNumUtil.pickByRandomRate(resultMap, configContent.getFinanceLedger().getLedgerStart(), configContent.getFinanceLedger().getLedgerEnd());
            chatUserTimeMapFinance = RandDayNumUtil.pickByRandomRate(financeLedgerMap, configContent.getFinanceLedger().getChatUserStart(), configContent.getFinanceLedger().getChatUserEnd());
        }
        if (!Objects.isNull( configContent.getCarLedger())) {
        //车险
        carLedgerMap = RandDayNumUtil.pickByRandomRate( resultMap, configContent.getCarLedger().getLedgerStart(), configContent.getCarLedger().getLedgerEnd());
       chatUserTimeMapCar = RandDayNumUtil.pickByRandomRate( carLedgerMap, configContent.getCarLedger().getChatUserStart(), configContent.getCarLedger().getChatUserEnd());
        }
        if (!Objects.isNull( configContent.getValueAddedLedger())) {
            //增值服务
            valueAddedLedgerMap = RandDayNumUtil.pickByRandomRate(resultMap, configContent.getValueAddedLedger().getLedgerStart(), configContent.getValueAddedLedger().getLedgerEnd());
            chatUserTimeMapValue = RandDayNumUtil.pickByRandomRate(valueAddedLedgerMap, configContent.getValueAddedLedger().getChatUserStart(), configContent.getValueAddedLedger().getChatUserEnd());
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //合作公司点击报表
        Map<String,ClickReport> clickReportMap = operateClickReport(dto,sdf,resultMap);
        Map<String,ClickReportHourly> clickReportHourMap = operateClickReportHour(dto,sdf,resultMap);
        List<PdCasualtyInfo> pdCasualtyInfoList = new ArrayList<>();
        List<PdCarInfo> carInfoList = new ArrayList<>();
        List<PdAdded> pdAddedList = new ArrayList<>();


        Integer tenantId = dto.getTenantId();

        List<PdLinkInfo> pdLinkInfoList = iPdLinkInfoService.lambdaQuery()
                .eq(PdLinkInfo::getTenantId, dto.getTenantId())
                .list();
        Map<Integer, Map<Integer, PdLinkInfo>> pdLinkInfoMapByTenantId = pdLinkInfoList.stream()
                .collect(Collectors.groupingBy(
                        PdLinkInfo::getTenantId, // 一级分组：tenantId
                        Collectors.toMap(
                                PdLinkInfo::getLinkType, // 二级分组：linkType
                                pd -> pd                 // 值：PdLinkInfo 对象
                        )
                ));
        //按照表格文件，参合台账
        saveByFileLedgers(files, financeLedgerMap, chatUserTimeMapFinance, pdCasualtyInfoList, configContent, pdLinkInfoMapByTenantId, carLedgerMap, chatUserTimeMapCar, carInfoList, random, valueAddedLedgerMap, chatUserTimeMapValue, pdAddedList);


        if (!CollectionUtils.isEmpty(pdCasualtyInfoList)){
            iPdCasualtyInfoService.saveBatch(pdCasualtyInfoList);
            updateChatCasualty( tenantId, pdCasualtyInfoList);
        }
        if (!CollectionUtils.isEmpty(pdAddedList)){
            iPdAddedService.saveBatch(pdAddedList);
            updateChatPdAdded( tenantId, pdAddedList);
        }
        if (!CollectionUtils.isEmpty(carInfoList)){

            iPdCarInfoService.saveBatch(carInfoList);
            updateChatCarInfo(tenantId, carInfoList);
        }
        if (!clickReportMap.isEmpty()){
            clickReportService.saveBatch(new ArrayList<>(clickReportMap.values()));
        }
        if (!clickReportHourMap.isEmpty()){
            clickReportHourlyService.saveBatch(new ArrayList<>(clickReportHourMap.values()));
        }
        List<ClickAutoPre> preList = getPreList(financeLedgerMap, carLedgerMap, valueAddedLedgerMap,
                configContent, dto.getTenantId(), chatUserTimeMapFinance, chatUserTimeMapCar, chatUserTimeMapValue);
        //生成剩余台账
        autoCreateLedger(preList, dto.getTenantId());
    }

    private void saveByFileLedgers(List<MultipartFile> files, Map<String, LocalDateTime> financeLedgerMap, Map<String, LocalDateTime> chatUserTimeMapFinance, List<PdCasualtyInfo> pdCasualtyInfoList, DailyConfigContent configContent, Map<Integer, Map<Integer, PdLinkInfo>> pdLinkInfoMapByTenantId, Map<String, LocalDateTime> carLedgerMap, Map<String, LocalDateTime> chatUserTimeMapCar, List<PdCarInfo> carInfoList, Random random, Map<String, LocalDateTime> valueAddedLedgerMap, Map<String, LocalDateTime> chatUserTimeMapValue, List<PdAdded> pdAddedList) {
         configContent = eaRegionService.formatCity(configContent.getCityRatios());
        List<ClickAutoPre> clickAutoPreList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(files)){
            for (MultipartFile file : files) {
                String filename = file.getOriginalFilename();
                if (filename == null) continue;

                if (filename.contains("财险")) {
                    List<PdInsuranceLedgerDTO> dtoList = ExcelUtils.read(file, PdInsuranceLedgerDTO.class);
                    // 按照传参的financeLedgerMap的size来限制处理的数据量
                    int processCount = Math.min(dtoList.size(), financeLedgerMap.size());

                    for (int i = 0; i < processCount; i++){
                        PdInsuranceLedgerDTO ledgerDTO = dtoList.get(i);
                        if (financeLedgerMap.isEmpty()){
                            break; // 如果Map已空，直接跳出循环
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(financeLedgerMap);

                        // 只设置时间和聊天用户标记，不生成随机值
                        ledgerDTO.setOrderDate(java.sql.Date.valueOf(entry.getValue().toLocalDate()));
                        if (chatUserTimeMapFinance.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        } else {
                            ledgerDTO.setHasChatUser(0);
                        }



                        // 转换为实体（不填充默认值）
                        PdInsuranceLedger insuranceLedger = convertToInsuranceEntityWithoutDefaults(ledgerDTO);

                        // 创建ClickAutoPre对象
                        ClickAutoPre clickAutoPre = new ClickAutoPre();
                        clickAutoPre.setTenantId(ledgerDTO.getTenantId());
                        clickAutoPre.setClickTime(Timestamp.valueOf(entry.getValue()));
                        clickAutoPre.setAutoCreate(1);
                        clickAutoPre.setHasChatUser(ledgerDTO.getHasChatUser());
                        clickAutoPre.setCity(configContent.getRandomCity());
                        clickAutoPre.setLedgerType(1); // 财险类型
                        clickAutoPre.setIsExcel(1); // 标记为表格导入
                        clickAutoPre.setPdInsuranceLedger(insuranceLedger); // 设置台账实体

                        clickAutoPreList.add(clickAutoPre);
                    }

                } else if (filename.contains("车险")) {
                    List<PdLedgerDTO> dtoList = ExcelUtils.read(file, PdLedgerDTO.class);
                    // 按照传参的carLedgerMap的size来限制处理的数据量
                    int processCount = Math.min(dtoList.size(), carLedgerMap.size());

                    for (int i = 0; i < processCount; i++) {
                        PdLedgerDTO ledgerDTO = dtoList.get(i);
                        if (carLedgerMap.isEmpty()){
                            break; // 如果Map已空，直接跳出循环
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(carLedgerMap);

                        // 只设置时间和聊天用户标记，不生成随机值
                        if (StringUtils.isEmpty(ledgerDTO.getSignDate())){
                            // 设置完整的日期时间格式，包含时分秒
                            ledgerDTO.setSignDate(entry.getValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            ledgerDTO.setSignDateTime(Timestamp.valueOf(entry.getValue()));
                        }else {
                            // 将日期字符串转换为标准格式后再创建 Timestamp
                            String formattedDate = formatDateForTimestamp(ledgerDTO.getSignDate());
                            ledgerDTO.setSignDateTime(Timestamp.valueOf(formattedDate));
                        }

                        if (chatUserTimeMapCar.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        } else {
                            ledgerDTO.setHasChatUser(0);
                        }

                        // 转换为实体（不填充默认值）
                        PdLedger vehicleLedger = convertToVehicleEntityWithoutDefaults(ledgerDTO);

                        // 创建ClickAutoPre对象
                        ClickAutoPre clickAutoPre = new ClickAutoPre();
                        clickAutoPre.setTenantId(ledgerDTO.getTenantId());
                        clickAutoPre.setClickTime(ledgerDTO.getSignDateTime());
                        clickAutoPre.setAutoCreate(1);
                        clickAutoPre.setHasChatUser(ledgerDTO.getHasChatUser());

    /**
     * 独立方法：根据总金额与点击，生成每日消耗（单价在区间内动态随机），并返回点击级别的记录列表
     */
    private List<ClickAutoPre> generateDailyConsumption(
            Map<String, LocalDateTime> resultMap,
            BigDecimal totalRechargeAmount,
            DailyConfigContent configContent,
            Integer tenantId) {

        BigDecimal minPrice = BigDecimal.valueOf(configContent.getPriceStart());
        BigDecimal maxPrice = BigDecimal.valueOf(configContent.getPriceEnd());

        // 1) 分组统计每日点击数
        Map<LocalDate, List<LocalDateTime>> dailyClicksMap = resultMap.values().stream()
                .collect(Collectors.groupingBy(LocalDateTime::toLocalDate));
        List<LocalDate> dates = dailyClicksMap.keySet().stream().sorted().collect(Collectors.toList());

        Map<LocalDate, Integer> clicks = new HashMap<>();
        for (LocalDate d : dates) clicks.put(d, dailyClicksMap.get(d).size());

        // 2) 可行性检查
        BigDecimal minTotal = BigDecimal.ZERO;
        BigDecimal maxTotal = BigDecimal.ZERO;
        for (LocalDate d : dates) {
            int c = clicks.get(d);
            minTotal = minTotal.add(minPrice.multiply(BigDecimal.valueOf(c)));
            maxTotal = maxTotal.add(maxPrice.multiply(BigDecimal.valueOf(c)));
        }
        if (totalRechargeAmount.compareTo(minTotal) < 0 || totalRechargeAmount.compareTo(maxTotal) > 0) {
            // 钳制目标总额，避免不可达
            totalRechargeAmount = totalRechargeAmount.compareTo(minTotal) < 0 ? minTotal : maxTotal;
        }

        // 3) 先随机生成每日单价（严格在区间内），再按比例缩放使总额匹配
        Random rnd = new Random();
        Map<LocalDate, BigDecimal> unitPrice = new HashMap<>();
        for (LocalDate d : dates) {
            double r = rnd.nextDouble();
            BigDecimal p = minPrice.add(maxPrice.subtract(minPrice).multiply(BigDecimal.valueOf(r)));
            unitPrice.put(d, p.setScale(4, BigDecimal.ROUND_HALF_UP));
        }

        // 4) 计算当前总额，与目标总额的比例，按比例缩放价格并钳制到区间内
        BigDecimal currentTotal = BigDecimal.ZERO;
        for (LocalDate d : dates) {
            currentTotal = currentTotal.add(unitPrice.get(d).multiply(BigDecimal.valueOf(clicks.get(d))));
        }
        if (currentTotal.compareTo(BigDecimal.ZERO) == 0) {
            // 极端情况：点击为0或价格为0
            currentTotal = BigDecimal.ONE;
        }
        BigDecimal scale = totalRechargeAmount.divide(currentTotal, 8, BigDecimal.ROUND_HALF_UP);
        for (LocalDate d : dates) {
            BigDecimal scaled = unitPrice.get(d).multiply(scale);
            if (scaled.compareTo(minPrice) < 0) scaled = minPrice;
            if (scaled.compareTo(maxPrice) > 0) scaled = maxPrice;
            unitPrice.put(d, scaled.setScale(4, BigDecimal.ROUND_HALF_UP));
        }

        // 5) 计算日金额并处理尾差（两位小数）
        Map<LocalDate, BigDecimal> dayAmount = new HashMap<>();
        BigDecimal sum = BigDecimal.ZERO;
        for (LocalDate d : dates) {
            BigDecimal amt = unitPrice.get(d).multiply(BigDecimal.valueOf(clicks.get(d))).setScale(2, BigDecimal.ROUND_HALF_UP);
            dayAmount.put(d, amt);
            sum = sum.add(amt);
        }
        BigDecimal residual = totalRechargeAmount.subtract(sum);
        if (residual.abs().compareTo(BigDecimal.valueOf(0.009)) >= 0) {
            // 以点击量大的日为优先，分币级别调整
            List<LocalDate> byClicksDesc = dates.stream()
                    .sorted((a, b) -> Integer.compare(clicks.get(b), clicks.get(a)))
                    .collect(Collectors.toList());
            BigDecimal step = residual.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.valueOf(0.01) : BigDecimal.valueOf(-0.01);
            for (LocalDate d : byClicksDesc) {
                if (residual.abs().compareTo(BigDecimal.valueOf(0.009)) < 0) break;
                // 调整该日金额
                BigDecimal newAmt = dayAmount.get(d).add(step);
                // 同步极小幅度调整单价，确保仍在区间内
                BigDecimal deltaPrice = step.divide(BigDecimal.valueOf(clicks.get(d)), 6, BigDecimal.ROUND_HALF_UP);
                BigDecimal newPrice = unitPrice.get(d).add(deltaPrice);
                if (newPrice.compareTo(minPrice) < 0 || newPrice.compareTo(maxPrice) > 0) continue;
                dayAmount.put(d, newAmt);
                unitPrice.put(d, newPrice);
                residual = residual.subtract(step);
            }
        }

        // 6) 生成点击级别记录
        DailyConfigContent cityList = eaRegionService.formatCity(configContent.getCityRatios());
        List<ClickAutoPre> list = new ArrayList<>();
        for (Map.Entry<String, LocalDateTime> e : resultMap.entrySet()) {
            LocalDate d = e.getValue().toLocalDate();
            BigDecimal p = unitPrice.get(d);
            BigDecimal amount = p.setScale(2, BigDecimal.ROUND_HALF_UP);
            ClickAutoPre rec = new ClickAutoPre()
                    .setClickTime(Timestamp.valueOf(e.getValue()))
                    .setCity(cityList.getRandomCity())
                    .setTenantId(tenantId)
                    .setAmount(amount);
            list.add(rec);
        }
        return list;
    }

                        clickAutoPre.setCity(configContent.getRandomCity());
                        clickAutoPre.setLedgerType(3); // 车险类型
                        clickAutoPre.setIsExcel(1); // 标记为表格导入
                        clickAutoPre.setPdLedger(vehicleLedger); // 设置台账实体

                        clickAutoPreList.add(clickAutoPre);
                    }
                } else if (filename.contains("增值服务")) {
                    List<PdAddedLedgerDTO> dtoList = ExcelUtils.read(file, PdAddedLedgerDTO.class);
                    // 按照传参的valueAddedLedgerMap的size来限制处理的数据量
                    int processCount = Math.min(dtoList.size(), valueAddedLedgerMap.size());

                    for (int i = 0; i < processCount; i++){
                        PdAddedLedgerDTO ledgerDTO = dtoList.get(i);
                        if (valueAddedLedgerMap.isEmpty()){
                            break; // 如果Map已空，直接跳出循环
                        }
                        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(valueAddedLedgerMap);

                        // 只设置时间和聊天用户标记，不生成随机值
                        ledgerDTO.setOrderDate(java.sql.Date.valueOf(entry.getValue().toLocalDate()));
                        if (chatUserTimeMapValue.containsKey(entry.getKey())){
                            ledgerDTO.setHasChatUser(1);
                        } else {
                            ledgerDTO.setHasChatUser(0);
                        }
                        // 转换为实体（不填充默认值）
                        PdAddedLedger addedLedger = convertToAddedEntityWithoutDefaults(ledgerDTO);

                        // 创建ClickAutoPre对象
                        ClickAutoPre clickAutoPre = new ClickAutoPre();
                        clickAutoPre.setTenantId(ledgerDTO.getTenantId());
                        // 处理 orderDate 转换为 Timestamp
                        if (ledgerDTO.getOrderDate() != null) {
                            // 将 java.sql.Date 转换为 Timestamp（设置时间为 00:00:00）
                            clickAutoPre.setClickTime(new Timestamp(ledgerDTO.getOrderDate().getTime()));
                        } else {
                            clickAutoPre.setClickTime(Timestamp.valueOf(entry.getValue()));
                        }
                        clickAutoPre.setAutoCreate(1);
                        clickAutoPre.setHasChatUser(ledgerDTO.getHasChatUser());
                        clickAutoPre.setCity(configContent.getRandomCity());
                        clickAutoPre.setLedgerType(2); // 增值服务类型
                        clickAutoPre.setIsExcel(1); // 标记为表格导入
                        clickAutoPre.setPdAddedLedger(addedLedger); // 设置台账实体

                        clickAutoPreList.add(clickAutoPre);
                    }
                }
            }
        }

        // 调用autoCreateLedgerInternal处理所有ClickAutoPre对象
        if (!clickAutoPreList.isEmpty()) {
            Integer tenantId = clickAutoPreList.get(0).getTenantId();
            autoCreateLedgerInternal(clickAutoPreList, tenantId);
        }
    }

    @Override
    public org.jeecg.modules.corp.vo.BatchImportResultVO validateMultipleFiles(MultipartFile file) {
        List<org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail> failureDetails = new ArrayList<>();

        try {
            // 1. 读取Excel文件，转换为DailyConfigExcelDTO列表
            InputStream inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);  // 跳过前两行（标题行）
            params.setHeadRows(1);   // 第一行作为头行
            params.setNeedSave(true);

            // 读取Excel内容为List<DailyConfigExcelDTO>类型
            List<DailyConfigExcelDTO> configList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);

            if (configList.isEmpty()) {
                return org.jeecg.modules.corp.vo.BatchImportResultVO.failure("导入的Excel文件没有数据", failureDetails);
            }

            // 2. 校验每一行数据
            int validCount = 0;
            for (int i = 0; i < configList.size(); i++) {
                DailyConfigExcelDTO config = configList.get(i);
                int rowNumber = i + 4; // 实际行号（考虑标题行）
                boolean hasValidationError = false;

                // 校验1：日期开始时间是否填写
                if (config.getMonthDataStart() == null) {
                    failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                        rowNumber, "", "日期开始时间未填写"
                    ));
                    hasValidationError = true;
                }

                // 校验2：租户是否存在
                if (config.getTenantId() != null) {
                    try {
                        SysTenant tenant = sysTenantService.getById(config.getTenantId());
                        if (tenant == null) {
                            failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                                rowNumber, "", "租户ID " + config.getTenantId() + " 不存在"
                            ));
                            hasValidationError = true;
                        }
                    } catch (Exception e) {
                        failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                            rowNumber, "", "租户ID " + config.getTenantId() + " 查询失败"
                        ));
                        hasValidationError = true;
                    }
                } else {
                    failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                        rowNumber, "", "租户ID未填写"
                    ));
                    hasValidationError = true;
                }

                // 校验3：城市和城市比例是否有填写
                if (config.getCities() != null && !config.getCities().isEmpty()) {
                    String[] cityNames = config.getCities().split(",");
                    String[] cityRatios = null;
                    if (config.getCityRatios() != null && !config.getCityRatios().isEmpty()) {
                        cityRatios = config.getCityRatios().split(",");
                    }

                    // 检查城市比例是否与城市数量匹配
                    if (cityRatios == null || cityRatios.length != cityNames.length) {
                        failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                            rowNumber, config.getCities(), "城市数量与城市比例数量不匹配"
                        ));
                        hasValidationError = true;
                    } else {
                        // 校验每个城市是否存在
                        for (int j = 0; j < cityNames.length; j++) {
                            String cityName = cityNames[j].trim();
                            if (!cityName.isEmpty()) {
                                // 查询城市是否存在
                                List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);
                                if (regions == null || regions.isEmpty()) {
                                    failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                                        rowNumber, cityName, "城市名称未找到匹配的编码"
                                    ));
                                    hasValidationError = true;
                                }

                                // 校验城市比例格式
                                if (j < cityRatios.length) {
                                    String ratioStr = cityRatios[j].trim();
                                    if (!ratioStr.isEmpty()) {
                                        try {
                                            Double.parseDouble(ratioStr);
                                        } catch (NumberFormatException e) {
                                            failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                                                rowNumber, cityName, "城市比例格式错误: " + ratioStr
                                            ));
                                            hasValidationError = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                        rowNumber, "", "城市信息未填写"
                    ));
                    hasValidationError = true;
                }

                if (!hasValidationError) {
                    validCount++;
                }
            }

            // 3. 返回校验结果
            if (failureDetails.isEmpty()) {
                return org.jeecg.modules.corp.vo.BatchImportResultVO.success(validCount);
            } else {
                return org.jeecg.modules.corp.vo.BatchImportResultVO.failure("校验失败，存在无效数据", failureDetails);
            }

        } catch (Exception e) {
            log.error("校验批量补全文件失败", e);
            failureDetails.add(new org.jeecg.modules.corp.vo.BatchImportResultVO.BatchImportFailureDetail(
                0, "", "文件读取失败: " + e.getMessage()
            ));
            return org.jeecg.modules.corp.vo.BatchImportResultVO.failure("校验失败", failureDetails);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadMultipleFiles(MultipartFile file) {
        try {
            // 1. 读取Excel文件，转换为DailyConfigExcelDTO列表
            InputStream inputStream = file.getInputStream();
            ImportParams params = new ImportParams();
            params.setTitleRows(2);  // 跳过前两行（标题行）
            params.setHeadRows(1);   // 第一行作为头行
            params.setNeedSave(true);

            // 读取Excel内容为List<DailyConfigExcelDTO>类型
            List<DailyConfigExcelDTO> configList = ExcelImportUtil.importExcel(inputStream, DailyConfigExcelDTO.class, params);
            configList.forEach(config -> {
                if (config.getMonthDataStart() == null){
                    throw new JeecgBootException("日期开始时间未填写,请确认是否为批量补全文件");
                }
            });

            if (configList.isEmpty()) {
                throw new IOException("导入的Excel文件没有数据");
            }

            // 2. 将配置信息转换为JSON并存入Redis的List结构中
            String redisKey = "tenant_batch_process:configs";
            // 清空之前的配置（如果有）
            redisTemplate.delete(redisKey);

            // 将每个配置转换为DailyConfigDto并存入Redis
            for (DailyConfigExcelDTO dto : configList) {
                // 创建DailyConfigDto对象
                DailyConfigDto dailyConfigDto = new DailyConfigDto();
                dailyConfigDto.setTenantId(dto.getTenantId());

                // 设置日期范围（使用我们的自定义转换方法）
                dailyConfigDto.setMonthDataStart(dto.getMonthDataStart());
                dailyConfigDto.setMonthDataEnd(dto.getMonthDataEnd());

                // 设置点击数
                dailyConfigDto.setClickNum(dto.getClickNum());

                // 构建配置JSON
                DailyConfigContent configContent = new DailyConfigContent();

                // 车险配置
                DailyConfigContent.Range carLedger = new DailyConfigContent.Range();
                carLedger.setLedgerStart(dto.getCarInsuranceStart());
                carLedger.setLedgerEnd(dto.getCarInsuranceEnd());
                carLedger.setChatUserStart(dto.getChatStart());
                carLedger.setChatUserEnd(dto.getChatEnd());
                configContent.setCarLedger(carLedger);

                // 财险配置
                DailyConfigContent.Range financeLedger = new DailyConfigContent.Range();
                financeLedger.setLedgerStart(dto.getPropertyInsuranceStart());
                financeLedger.setLedgerEnd(dto.getPropertyInsuranceEnd());
                financeLedger.setChatUserStart(dto.getPropertyChatStart());
                financeLedger.setChatUserEnd(dto.getPropertyChatEnd());
                configContent.setFinanceLedger(financeLedger);

                // 增值服务配置
                DailyConfigContent.Range valueAddedLedger = new DailyConfigContent.Range();
                valueAddedLedger.setLedgerStart(dto.getValueServiceStart());
                valueAddedLedger.setLedgerEnd(dto.getValueServiceEnd());
                valueAddedLedger.setChatUserStart(dto.getValueChatStart());
                valueAddedLedger.setChatUserEnd(dto.getValueChatEnd());
                configContent.setValueAddedLedger(valueAddedLedger);

                // 城市配置处理：将城市比例也存储到configJson中去
                // 表格导入的城市比例cityRatios和cities城市是一一对应的，按照城市逗号隔开和城市占比比例用逗号隔开，顺序对应
                // 表格内的值为 20即为 20%，0.1即为 0.1%
                if (dto.getCities() != null && !dto.getCities().isEmpty()) {
                    // 将逗号分隔的城市字符串转换为List<String>类型，存储城市编码
                    List<String> cityList = new ArrayList<>();
                    List<DailyConfigContent.CityRatio> cityRatioList = new ArrayList<>();

                    // 按逗号分割城市名称
                    String[] cityNames = dto.getCities().split(",");

                    // 按逗号分割城市比例
                    String[] cityRatios = null;
                    if (dto.getCityRatios() != null && !dto.getCityRatios().isEmpty()) {
                        cityRatios = dto.getCityRatios().split(",");
                    }

                    // 遍历每个城市名称，查询对应的城市编码并处理比例
                    for (int i = 0; i < cityNames.length; i++) {
                        String cityName = cityNames[i].trim();
                        if (!cityName.isEmpty()) {
                            // 根据城市名称模糊查询城市信息
                            List<EaRegion> regions = eaRegionService.findByCityNameLike(cityName);

                            // 如果找到匹配的城市，添加其编码到列表中
                            if (regions != null && !regions.isEmpty()) {
                                // 取第一个匹配的城市编码
                                String cityCode = regions.get(0).getCode();
                                cityList.add(cityCode);
                                log.info("城市 [{}] 匹配到编码: {}", cityName, cityCode);

                                // 处理对应的城市比例
                                if (cityRatios != null && i < cityRatios.length) {
                                    String ratioStr = cityRatios[i].trim();
                                    if (!ratioStr.isEmpty()) {
                                        try {
                                            Double ratio = Double.parseDouble(ratioStr);
                                            // 创建城市比例对象
                                            DailyConfigContent.CityRatio cityRatio = new DailyConfigContent.CityRatio();
                                            cityRatio.setCityCode(cityCode);
                                            cityRatio.setRatio(ratio);
                                            cityRatioList.add(cityRatio);
                                            log.info("城市 [{}] 编码: {} 比例: {}%", cityName, cityCode, ratio);
                                        } catch (NumberFormatException e) {
                                            log.warn("城市 [{}] 比例格式错误: {}", cityName, ratioStr);
                                        }
                                    }
                                }
                            } else {
                                log.warn("城市 [{}] 未找到匹配的编码", cityName);
                            }
                        }
                    }

                    // 设置城市编码列表
                    configContent.setCityList(cityList);

                    // 设置城市比例列表
                    configContent.setCityRatios(cityRatioList);

                    // 使用formatCity方法处理一级城市编码，将其转换为二级城市编码并分配比例
                    DailyConfigContent formattedCityConfig = eaRegionService.formatCity(cityRatioList);
                    if (formattedCityConfig != null && formattedCityConfig.getCityRatios() != null) {
                        // 更新配置内容为格式化后的城市比例
                        configContent.setCityRatios(formattedCityConfig.getCityRatios());
                        log.info("城市比例格式化完成，一级城市已转换为二级城市，共 {} 个城市比例配置", formattedCityConfig.getCityRatios().size());
                    }

                    log.info("处理城市列表完成，共 {} 个城市编码，{} 个城市比例配置", cityList.size(), cityRatioList.size());
                }

                // 将配置内容转为JSON
                dailyConfigDto.setConfigJson(objectMapper.writeValueAsString(configContent));

                // 将DailyConfigDto对象转为JSON字符串并存入Redis
                String configJson = objectMapper.writeValueAsString(dailyConfigDto);
                redisTemplate.opsForList().rightPush(redisKey, configJson);
            }

            // 记录初始任务总数
            Long totalTasks = redisTemplate.opsForList().size(redisKey);
            redisTemplate.opsForValue().set("tenant_batch_process:total_tasks", totalTasks.toString());
            log.info("批量处理任务初始化: 总任务数={}", totalTasks);

            // 3. 启动异步处理任务
            processTenantConfigsAsync();

            log.info("成功将{}个租户配置加入处理队列，开始异步处理", configList.size());
        } catch (Exception e) {
            log.error("处理批量补全文件失败", e);
            throw new JeecgBootException("处理批量补全文件失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理租户配置
     * 从Redis队列中逐个取出租户配置并处理
     */
    @Override
    public void processTenantConfigsAsync() {
        // 创建并启动一个新线程来处理队列
        Thread processThread = new Thread(() -> {
            String redisKey = "tenant_batch_process:configs";
            String processingKey = "tenant_batch_process:processing";
            String currentTenantKey = "tenant_batch_process:current_tenant";
            String completedTenantsKey = "tenant_batch_process:completed_tenants";

            try {
                // 设置处理状态为进行中
                redisTemplate.opsForValue().set(processingKey, "1");

                // 清空已完成租户列表
                redisTemplate.delete(completedTenantsKey);

                // 设置Redis过期时间到明天凌晨1点
                Calendar tomorrowEarly = Calendar.getInstance();
                tomorrowEarly.add(Calendar.DAY_OF_MONTH, 1);
                tomorrowEarly.set(Calendar.HOUR_OF_DAY, 1);
                tomorrowEarly.set(Calendar.MINUTE, 0);
                tomorrowEarly.set(Calendar.SECOND, 0);

                // 计算从现在到明天凌晨1点的秒数
                long secondsUntilTomorrowEnd = (tomorrowEarly.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(redisKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentTenantKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedTenantsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);


                while (true) {
                    // 从队列左侧弹出一个配置（先进先出）
                    String configJson = (String) redisTemplate.opsForList().leftPop(redisKey);

                    // 如果队列为空，处理完成
                    if (configJson == null) {
                        // 清除当前处理的租户信息
                        redisTemplate.delete(currentTenantKey);
                        break;
                    }

                    try {
                        // 将JSON直接转换回DailyConfigDto对象
                        DailyConfigDto dailyConfigDto = objectMapper.readValue(configJson, DailyConfigDto.class);
                        Integer tenantId = dailyConfigDto.getTenantId();

                        // 获取租户名称
                        String tenantName = "未知租户";
                        try {
                            // 查询租户信息
                            SysTenant tenant = sysTenantService.getById(tenantId);
                            if (tenant != null) {
                                tenantName = tenant.getName();
                            }
                        } catch (Exception e) {
                            log.error("获取租户名称失败", e);
                        }

                        // 记录开始时间
                        long startTime = System.currentTimeMillis();

                        // 获取点击数
                        int clickCount = 0;
                        try {
                            // 从配置中获取点击数
                            DailyConfigContent configContent = objectMapper.readValue(dailyConfigDto.getConfigJson(), DailyConfigContent.class);
                            if (configContent != null) {
                                // 获取总点击数
                                clickCount = dailyConfigDto.getClickNum() != null ? dailyConfigDto.getClickNum() : 0;
                            }
                        } catch (Exception e) {
                            log.error("获取点击数失败", e);
                        }

                        // 记录当前处理的租户信息
                        Map<String, Object> currentTenantInfo = new HashMap<>();
                        currentTenantInfo.put("tenantId", tenantId);
                        currentTenantInfo.put("tenantName", tenantName);
                        currentTenantInfo.put("startTime", startTime);
                        currentTenantInfo.put("clickCount", clickCount); // 添加点击数

                        // 将当前租户信息存入Redis
                        redisTemplate.opsForValue().set(currentTenantKey, objectMapper.writeValueAsString(currentTenantInfo));


                        // 调用importLedgers方法处理单个租户
                        this.importLedgers(null, dailyConfigDto);

                        // 记录结束时间和耗时
                        long endTime = System.currentTimeMillis();
                        long duration = endTime - startTime;

                        // 记录已完成的租户信息
                        Map<String, Object> completedTenantInfo = new HashMap<>();
                        completedTenantInfo.put("tenantId", tenantId);
                        completedTenantInfo.put("tenantName", tenantName);
                        completedTenantInfo.put("startTime", startTime);
                        completedTenantInfo.put("endTime", endTime);
                        completedTenantInfo.put("duration", duration);

                        // 从当前租户信息中获取点击数
                        Object currentTenantObj = redisTemplate.opsForValue().get(currentTenantKey);
                        if (currentTenantObj != null) {
                            try {
                                Map<String, Object> currentTenant = objectMapper.readValue(currentTenantObj.toString(),
                                    new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});
                                if (currentTenant.containsKey("clickCount")) {
                                    completedTenantInfo.put("clickCount", currentTenant.get("clickCount"));
                                }
                            } catch (Exception e) {
                                log.error("获取当前租户点击数失败", e);
                                completedTenantInfo.put("clickCount", 0);
                            }
                        } else {
                            completedTenantInfo.put("clickCount", 0);
                        }

                        // 将已完成的租户信息添加到列表
                        redisTemplate.opsForList().rightPush(completedTenantsKey, objectMapper.writeValueAsString(completedTenantInfo));


                        // 添加适当的延迟，避免系统负载过高
                        Thread.sleep(1000);
                    } catch (Exception e) {
                        log.error("处理租户配置失败: {}", configJson, e);
                        // 处理失败的配置放回队列末尾，以便稍后重试
                        redisTemplate.opsForList().rightPush(redisKey, configJson);
                        // 添加延迟，避免立即重试
                        Thread.sleep(5000);
                    }
                }

                // 设置处理状态为已完成
                redisTemplate.opsForValue().set(processingKey, "0");

                // 确保所有数据在任务完成后保留到明天凌晨1点
                Calendar tomorrowEarly2 = Calendar.getInstance();
                tomorrowEarly2.add(Calendar.DAY_OF_MONTH, 1);
                tomorrowEarly2.set(Calendar.HOUR_OF_DAY, 1);
                tomorrowEarly2.set(Calendar.MINUTE, 0);
                tomorrowEarly2.set(Calendar.SECOND, 0);

                // 计算从现在到明天凌晨1点的秒数
                long secondsUntilEnd = (tomorrowEarly2.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                // 设置Redis过期时间
                redisTemplate.expire(processingKey, secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire(currentTenantKey, secondsUntilEnd, TimeUnit.SECONDS);
                redisTemplate.expire(completedTenantsKey, secondsUntilEnd, TimeUnit.SECONDS);

            } catch (Exception e) {
                log.error("租户配置批处理失败", e);
                // 设置处理状态为失败
                try {
                    redisTemplate.opsForValue().set(processingKey, "-1");
                } catch (Exception ex) {
                    log.error("设置处理状态失败", ex);
                }
            }
        });

        // 设置为守护线程，不阻止JVM退出
        processThread.setDaemon(true);
        processThread.setName("TenantBatchProcessor");
        processThread.start();
    }

    //操作合作公司点击报表
    public Map<String,ClickReport>  operateClickReport(DailyConfigDto dto,SimpleDateFormat sdf,Map<String, LocalDateTime> resultMap){
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());
        //todo 合作公司点击报表
//        clickReportService.lambdaUpdate()
//                .between(ClickReport::getStatDate, java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant())
//                        , java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant()))
//                .eq(ClickReport::getTenantId,dto.getTenantId()).remove();

        Map<String, ClickReport> clickReportMap = new HashMap<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(monthDataStartDate);

        // 结束时间
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(monthDataEndDate);

        while (!calendar.after(endCalendar)) {
            Date currentDate = calendar.getTime();
            // 格式化当前日期为字符串作为键
            String dateKey = sdf.format(currentDate);
            // 如果当天不存在，创建新的 ClickReport
            if (!clickReportMap.containsKey(dateKey)) {
                ClickReport newReport = new ClickReport();
                newReport.setStatDate(currentDate);
                // 这里可以设置其他默认值，比如 newReport.setXXX(默认值);
                newReport.setClickNum(0);
                newReport.setCompanyId(dto.getTenantId().toString());
                newReport.setTenantId(dto.getTenantId());
                clickReportMap.put(dateKey, newReport);
            }
            // 日期 +1天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (String key : resultMap.keySet()){
            ClickReport clickReport = clickReportMap.get(resultMap.get(key).format(formatter));
            clickReport.setClickNum(clickReport.getClickNum() == null ? 1 : clickReport.getClickNum() + 1);

        }
        return clickReportMap;
    }


    //操作合作公司点击报表
    public Map<String,ClickReportHourly>  operateClickReportHour(DailyConfigDto dto,SimpleDateFormat sdf,Map<String, LocalDateTime> resultMap){
        Date monthDataStartDate = java.sql.Date.from(dto.getMonthDataStart().atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date monthDataEndDate = java.sql.Date.from(dto.getMonthDataEnd().atStartOfDay(ZoneId.systemDefault()).toInstant());

        Map<String, ClickReportHourly> clickReportHourlyMap = new HashMap<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(monthDataStartDate);

        // 结束时间
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(monthDataEndDate);

        while (!calendar.after(endCalendar)) {
            // 格式化当前日期为字符串作为键
            for (int i = 0; i < 24; i++) {
                Date currentDate = calendar.getTime();
                String dateKey = sdf.format(currentDate)+ i;
                // 如果当天不存在，创建新的 ClickReport
                if (!clickReportHourlyMap.containsKey(dateKey)) {
                    ClickReportHourly newReport = new ClickReportHourly();
                    newReport.setStatDate(currentDate);
                    // 这里可以设置其他默认值，比如 newReport.setXXX(默认值);
                    newReport.setClickNum(0);
                    newReport.setClickPv(0); // 初始化 clickPv
                    newReport.setHour(i);
                    newReport.setTenantId(dto.getTenantId());
                    clickReportHourlyMap.put(dateKey, newReport);
                }
            }
            // 日期 +1天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        // 获取 PV/UV 比值配置
        DeployConfigDTO deployConfig = null;
        try {
            deployConfig = deployConfigService.getDeployConfig();
        } catch (Exception e) {
            log.warn("获取配置失败，将使用默认随机生成方式: {}", e.getMessage());
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (String key : resultMap.keySet()){
            LocalDateTime ldt = resultMap.get(key);
            String dateKey = ldt.format(formatter) + ldt.getHour(); // 拼接日期+小时作为key
            ClickReportHourly clickReport = clickReportHourlyMap.get(dateKey);

            // 更新 UV (clickNum)
            int currentClickNum = clickReport.getClickNum() == null ? 1 : clickReport.getClickNum() + 1;
            clickReport.setClickNum(currentClickNum);

            // 生成 PV (clickPv)
            int generatedClickPv = generateClickPv(currentClickNum, deployConfig);
            clickReport.setClickPv((clickReport.getClickPv() == null ? 0 : clickReport.getClickPv()) + generatedClickPv);
        }
        return clickReportHourlyMap;
    }

    /**
     * 根据配置生成 PV 值
     * @param uvCount UV 数量（独立访客数）
     * @param deployConfig 部署配置
     * @return 生成的 PV 值
     */
    private int generateClickPv(int uvCount, DeployConfigDTO deployConfig) {
        // 如果 UV 为 0，PV 也为 0
        if (uvCount <= 0) {
            return 0;
        }

        BigDecimal pvUvRatioMin;
        BigDecimal pvUvRatioMax;

        // 检查是否有配置
        if (deployConfig != null && deployConfig.getPvUvRatioMin() != null && deployConfig.getPvUvRatioMax() != null) {
            pvUvRatioMin = deployConfig.getPvUvRatioMin();
            pvUvRatioMax = deployConfig.getPvUvRatioMax();
            log.debug("使用配置的 PV/UV 比值区间: {} - {}", pvUvRatioMin, pvUvRatioMax);
        } else {
            // 没有配置时使用默认区间
            pvUvRatioMin = new BigDecimal("1.5");
            pvUvRatioMax = new BigDecimal("4.0");
            log.debug("使用默认 PV/UV 比值区间: {} - {}", pvUvRatioMin, pvUvRatioMax);
        }

        // 确保最小值不大于最大值
        if (pvUvRatioMin.compareTo(pvUvRatioMax) > 0) {
            BigDecimal temp = pvUvRatioMin;
            pvUvRatioMin = pvUvRatioMax;
            pvUvRatioMax = temp;
        }

        // 生成随机比值，增加随机性
        double randomRatio = generateRandomRatio(pvUvRatioMin.doubleValue(), pvUvRatioMax.doubleValue());

        // 计算 PV 值
        double pvValue = uvCount * randomRatio;

        // 添加小幅度随机波动，使数据更真实
        double fluctuation = ThreadLocalRandom.current().nextGaussian() * 0.1; // 标准差为 0.1 的正态分布
        pvValue = pvValue * (1 + fluctuation);

        // 确保 PV 值不小于 UV 值
        int result = Math.max(uvCount, (int) Math.round(pvValue));

        log.debug("UV: {}, 随机比值: {:.3f}, 生成的 PV: {}", uvCount, randomRatio, result);

        return result;
    }

    /**
     * 生成随机比值，使用多种随机算法增加真实性
     * @param min 最小值
     * @param max 最大值
     * @return 随机比值
     */
    private double generateRandomRatio(double min, double max) {
        ThreadLocalRandom random = ThreadLocalRandom.current();

        // 使用不同的随机分布策略，增加数据的真实性
        int strategy = random.nextInt(3);

        switch (strategy) {
            case 0:
                // 均匀分布
                return random.nextDouble(min, max);

            case 1:
                // 偏向中间值的分布
                double mid = (min + max) / 2;
                double range = (max - min) / 4;
                double gaussian = random.nextGaussian() * range + mid;
                return Math.max(min, Math.min(max, gaussian));

            case 2:
                // 偏向较小值的分布（符合实际业务场景）
                double skewed = Math.pow(random.nextDouble(), 1.5); // 偏向小值
                return min + skewed * (max - min);

            default:
                return random.nextDouble(min, max);
        }
    }

    private PdInsuranceLedger convertToInsuranceEntity(PdInsuranceLedgerDTO dto) {
        PdInsuranceLedger entity = new PdInsuranceLedger();
        BeanUtils.copyProperties(dto, entity);
        // 为空字段生成默认值
        fillInsuranceLedgerDefaults(entity);
        return entity;
    }

    /**
     * 转换为财险台账实体（不填充默认值，用于表格导入）
     */
    private PdInsuranceLedger convertToInsuranceEntityWithoutDefaults(PdInsuranceLedgerDTO dto) {
        PdInsuranceLedger entity = new PdInsuranceLedger();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    private PdLedger convertToVehicleEntity(PdLedgerDTO dto) {
        PdLedger entity = new PdLedger();
        BeanUtils.copyProperties(dto, entity);
        // 为空字段生成默认值
        fillVehicleLedgerDefaults(entity);
        return entity;
    }

    /**
     * 转换为车险台账实体（不填充默认值，用于表格导入）
     */
    private PdLedger convertToVehicleEntityWithoutDefaults(PdLedgerDTO dto) {
        PdLedger entity = new PdLedger();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    private PdAddedLedger convertToAddedEntity(PdAddedLedgerDTO dto) {
        PdAddedLedger entity = new PdAddedLedger();
        BeanUtils.copyProperties(dto, entity);
        // 为空字段生成默认值
        fillAddedLedgerDefaults(entity);
        return entity;
    }

    /**
     * 转换为增值服务台账实体（不填充默认值，用于表格导入）
     */
    private PdAddedLedger convertToAddedEntityWithoutDefaults(PdAddedLedgerDTO dto) {
        PdAddedLedger entity = new PdAddedLedger();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 为财险台账实体填充空字段的默认值
     * @param entity 财险台账实体
     */
    private void fillInsuranceLedgerDefaults(PdInsuranceLedger entity) {
        // 如果手机号为空，生成随机手机号
        if (StringUtils.isBlank(entity.getPhone())) {
            entity.setPhone(ProvinceIpGenerator.generateMaskedPhone());
        }

        // 如果姓名为空，生成随机姓名
        if (StringUtils.isBlank(entity.getName())) {
            String name = sceneService.getRandomName();
            // 随机添加先生/女士后缀
            Random random = new Random();
            String surname = name.substring(0, 1);
            String title = random.nextBoolean() ? "先生" : "女士";
            entity.setName(surname + title);
        }

        // 如果服务项目为空，随机选择一个财险服务
        if (StringUtils.isBlank(entity.getUserItem())) {
            String randomServiceId = pdIntegratedService.getRandomServiceId(1);
            entity.setUserItem(randomServiceId);
        }

        // 设置默认状态
        if (entity.getIsVied() == null) {
            entity.setIsVied(0);
        }
    }

    /**
     * 为车险台账实体填充空字段的默认值
     * @param entity 车险台账实体
     */
    private void fillVehicleLedgerDefaults(PdLedger entity) {
        // 如果险种名称为空，生成随机险种
        if (StringUtils.isBlank(entity.getInsuranceName())) {
            entity.setInsuranceName(RandomInsuranceTypeGenerator.generateRandomInsuranceType());
        }

        // 如果手机号为空，生成随机手机号
        if (StringUtils.isBlank(entity.getPhoneNumber())) {
            entity.setPhoneNumber(ProvinceIpGenerator.generateMaskedPhone());
        }

        // 如果厂牌型号为空，生成随机品牌
        if (StringUtils.isBlank(entity.getBrandModel())) {
            entity.setBrandModel(RandomCarBrandGenerator.generateRandomCarBrand());
        }

        // 如果车架号为空，生成随机车架号
        if (StringUtils.isBlank(entity.getVin())) {
            entity.setVin(RandomVinGenerator.generateRandomVin());
        }

        // 如果投保人为空，生成随机姓名
        if (StringUtils.isBlank(entity.getPolicyholder())) {
            String name = sceneService.getRandomName();
            Random random = new Random();
            String surname = name.substring(0, 1);
            String title = random.nextBoolean() ? "先生" : "女士";
            entity.setPolicyholder(surname + title);
        }

        // 如果被保人为空，使用投保人姓名
        if (StringUtils.isBlank(entity.getInsured())) {
            entity.setInsured(entity.getPolicyholder());
        }

        // 如果车牌号为空，根据城市生成车牌号
        if (StringUtils.isBlank(entity.getLicensePlate())) {
            // 这里需要一个默认城市，或者从配置中获取
            String defaultCity = "福建省"; // 可以根据实际需求调整
            entity.setLicensePlate(ChinaPlateNumberGenerator.generatePlateNumber(defaultCity));
        }

        // 设置默认状态
        entity.setChatStatus(0);
        entity.setIsDelete("0");
    }

    /**
     * 为增值服务台账实体填充空字段的默认值
     * @param entity 增值服务台账实体
     */
    private void fillAddedLedgerDefaults(PdAddedLedger entity) {
        // 如果手机号为空，生成随机手机号
        if (StringUtils.isBlank(entity.getPhone())) {
            entity.setPhone(ProvinceIpGenerator.generateMaskedPhone());
        }

        // 如果姓名为空，生成随机姓名
        if (StringUtils.isBlank(entity.getName())) {
            String name = sceneService.getRandomName();
            Random random = new Random();
            String surname = name.substring(0, 1);
            String title = random.nextBoolean() ? "先生" : "女士";
            entity.setName(surname + title);
        }

        // 如果服务项目为空，随机选择一个增值服务
        if (StringUtils.isBlank(entity.getUserItem())) {
            String randomServiceId = pdIntegratedService.getRandomServiceId(2);
            entity.setUserItem(randomServiceId);
        }

        // 设置默认状态
        if (entity.getIsVied() == null) {
            entity.setIsVied(0);
        }
    }

    /**
     * 根据配置对数据进行脱敏处理
     * @param data 原始数据
     * @param fieldType 字段类型（name、phone、licensePlate、vin）
     * @param deployConfig 部署配置
     * @return 处理后的数据
     */
    private String applyDesensitization(String data, String fieldType, DeployConfigDTO deployConfig) {
        if (StringUtils.isBlank(data) || deployConfig == null) {
            return data;
        }

        // 只有当数据实现方式是"数据库存储"时才进行脱敏处理
        // 如果是"查询实现脱敏"，则在查询时处理，这里不处理
        if (!"db".equals(deployConfig.getQueryType())) {
            return data;
        }

        switch (fieldType) {
            case "name":
                // 姓名脱敏：如果开启了姓名切换为先生/女士
                if (Boolean.TRUE.equals(deployConfig.getNameSwitch())) {
                    // 随机选择先生或女士
                    Random random = new Random();
                    String surname = data.substring(0, 1);
                    String title = random.nextBoolean() ? "先生" : "女士";
                    return surname + title;
                }
                break;

            case "phone":
                // 手机号脱敏：如果开启了手机号脱敏
                if (Boolean.TRUE.equals(deployConfig.getPhoneSwitch())) {
                    return SensitiveInfoUtil.mobilePhone(data);
                }
                break;

            case "licensePlate":
                // 车牌号脱敏：如果开启了车牌号脱敏
                if (Boolean.TRUE.equals(deployConfig.getPlateNoMask())) {
                    // 车牌号脱敏：显示前2位和后2位，中间用*代替
                    if (data.length() >= 4) {
                        String prefix = data.substring(0, 2);
                        String suffix = data.substring(data.length() - 2);
                        String stars = String.join("", Collections.nCopies(data.length() - 4, "*"));
                        return prefix + stars + suffix;
                    }
                }
                break;

            case "vin":
                // 车架号脱敏：如果开启了车架号脱敏
                if (Boolean.TRUE.equals(deployConfig.getVinNoMask())) {
                    // 车架号脱敏：显示前4位和后4位，中间用*代替
                    if (data.length() >= 8) {
                        String prefix = data.substring(0, 4);
                        String suffix = data.substring(data.length() - 4);
                        String stars = String.join("", Collections.nCopies(data.length() - 8, "*"));
                        return prefix + stars + suffix;
                    }
                }
                break;

            default:
                break;
        }

        return data;
    }

    public Map.Entry<String, LocalDateTime> pickAndRemoveRandomEntry(Map<String, LocalDateTime> map) {
        if (map == null || map.isEmpty()) return null;

        int randomIndex = new Random().nextInt(map.size());
        Iterator<Map.Entry<String, LocalDateTime>> iterator = map.entrySet().iterator();

        for (int i = 0; i < randomIndex; i++) {
            iterator.next();
        }

        Map.Entry<String, LocalDateTime> entry = iterator.next();
        iterator.remove(); // 从 map 中移除这个元素

        return entry;
    }

    /**
     * 将各种日期格式转换为 Timestamp.valueOf() 所需的标准格式
     * @param dateStr 输入的日期字符串，支持格式如：2025/8/22 4:23:56, 2025-8-22 4:23 等
     * @return 标准格式的日期字符串 yyyy-MM-dd HH:mm:ss
     */
    private String formatDateForTimestamp(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        try {
            // 定义可能的输入格式，按照最可能匹配的顺序排列
            String[] inputFormats = {
                "yyyy/M/d H:m:s",      // 2025/8/22 4:23:56
                "yyyy-M-d H:m:s",      // 2025-8-22 4:23:56
                "yyyy/M/d H:m",        // 2025/8/22 4:23
                "yyyy-M-d H:m",        // 2025-8-22 4:23
                "yyyy/MM/dd HH:mm:ss", // 2025/08/22 04:23:56
                "yyyy-MM-dd HH:mm:ss", // 2025-08-22 04:23:56 (标准格式)
                "yyyy/MM/dd HH:mm",    // 2025/08/22 04:23
                "yyyy-MM-dd HH:mm",    // 2025-08-22 04:23
                "yyyy-M-d HH:mm",      // 2025-8-22 04:23 (补充格式)
                "yyyy/M/d HH:mm",      // 2025/8/22 04:23 (补充格式)
                "yyyy/M/d",            // 2025/8/22
                "yyyy-M-d",            // 2025-8-22
                "yyyy/MM/dd",          // 2025/08/22
                "yyyy-MM-dd"           // 2025-08-22
            };

            // 输出格式
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 尝试解析各种格式
            for (String format : inputFormats) {
                try {
                    SimpleDateFormat inputFormat = new SimpleDateFormat(format);
                    inputFormat.setLenient(false);
                    Date date = inputFormat.parse(dateStr.trim());
                    return outputFormat.format(date);
                } catch (ParseException e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果所有格式都失败，抛出异常
            throw new JeecgBootException("无法解析日期格式: " + dateStr + "，支持的格式包括：yyyy/M/d H:m:s, yyyy-M-d H:m, yyyy-MM-dd HH:mm:ss 等");

        } catch (Exception e) {
            log.error("日期格式转换失败: {}", dateStr, e);
            throw new JeecgBootException("日期格式转换失败: " + dateStr + ", 错误: " + e.getMessage());
        }
    }

    public java.sql.Date getRandTime(Map<String, LocalDateTime> map){
        if (map.isEmpty()){
            throw new JeecgBootException("台账区间配置异常");
        }
        Map.Entry<String, LocalDateTime> entry = pickAndRemoveRandomEntry(map);
        // 转换：只保留日期部分（不含时间）
        return java.sql.Date.valueOf(entry.getValue().toLocalDate());
    }


    public List<ClickAutoPre> getPreList(Map<String, LocalDateTime> financeLedgerMap,Map<String, LocalDateTime> carLedgerMap,Map<String, LocalDateTime> valueAddedLedgerMap,
                                         DailyConfigContent configContent,Integer tenantId,Map<String, LocalDateTime> chatUserTimeMapFinance,
                                         Map<String, LocalDateTime> chatUserTimeMapCar,Map<String, LocalDateTime> chatUserTimeMapValue){
        List<ClickAutoPre> preList = new ArrayList<>();
        preList.addAll(setMapData( financeLedgerMap, configContent, tenantId, 1,chatUserTimeMapFinance));
        preList.addAll(setMapData( carLedgerMap, configContent, tenantId, 3,chatUserTimeMapCar));
        preList.addAll(setMapData( valueAddedLedgerMap, configContent, tenantId, 2,chatUserTimeMapValue));
        return preList;
    }

    public List<ClickAutoPre> setMapData(Map<String, LocalDateTime> map,DailyConfigContent configContent,Integer tenantId,Integer ledgerType,Map<String, LocalDateTime> chatUserMap){
        //城市格式化
        DailyConfigContent cityList = eaRegionService.formatCity(configContent.getCityRatios());
        List<ClickAutoPre> list = new ArrayList<>();
        for (String key : map.keySet()){
            ClickAutoPre clickAutoPre = new ClickAutoPre()
                    .setAutoCreate(1)
                    .setClickTime(Timestamp.valueOf(map.get(key)))
                    .setCity(cityList.getRandomCity())
                    .setTenantId(tenantId)
                    .setLedgerType(ledgerType);
            if (chatUserMap.containsKey(key)){
                clickAutoPre.setHasChatUser(1);
            }
            list.add(clickAutoPre);
        }
        return list;
    }

    /**
     * 生成ClickAutoPre数据（不保存充值记录和日消耗明细）
     * 修复：正确计算每日消耗 = 点击数 × 单价，确保总额等于totalRechargeAmount
     */
    public List<ClickAutoPre> setMapDataWithoutRecharge(Map<String, LocalDateTime> map,DailyConfigContent configContent,Integer tenantId,Integer ledgerType,Map<String, LocalDateTime> chatUserMap){
        //城市格式化
        DailyConfigContent cityList = eaRegionService.formatCity(configContent.getCityRatios());

        // 计算总充值金额
        BigDecimal totalRechargeAmount = calculateTotalRechargeAmount(configContent);

        // 生成每日单价分配（修复后的方法）
        Map<LocalDateTime, BigDecimal> dailyUnitPriceMap = generateDailyUnitPriceAllocation(
            map, totalRechargeAmount, configContent.getPriceStart(), configContent.getPriceEnd());

        // 生成ClickAutoPre列表
        List<ClickAutoPre> list = new ArrayList<>();
        for (String key : map.keySet()){
            LocalDateTime dateTime = map.get(key);
            BigDecimal unitPrice = dailyUnitPriceMap.get(dateTime);

            // 每个点击的消耗金额 = 单价 × 1（每个点击数量为1）
            BigDecimal clickAmount = unitPrice.setScale(2, BigDecimal.ROUND_HALF_UP);

            ClickAutoPre clickAutoPre = new ClickAutoPre()
                    .setAutoCreate(1)
                    .setClickTime(Timestamp.valueOf(dateTime))
                    .setCity(cityList.getRandomCity())
                    .setTenantId(tenantId)
                    .setLedgerType(ledgerType)
                    .setAmount(clickAmount); // 设置每个点击的消耗金额
            if (chatUserMap.containsKey(key)){
                clickAutoPre.setHasChatUser(1);
            }
            list.add(clickAutoPre);
        }

        // 验证并修正尾差
        list = fixAmountResidual(list, totalRechargeAmount, configContent.getPriceStart(), configContent.getPriceEnd());

        return list;
    }

    @Override
    public Map<String, Object> getBatchProcessProgress() {
        Map<String, Object> progressInfo = new HashMap<>();
        String redisKey = "tenant_batch_process:configs";
        String processingKey = "tenant_batch_process:processing";
        String currentTenantKey = "tenant_batch_process:current_tenant";
        String completedTenantsKey = "tenant_batch_process:completed_tenants";

        try {
            // 获取队列中剩余的任务数
            Long remainingTasks = redisTemplate.opsForList().size(redisKey);

            // 获取处理状态
            Object processingStatus = redisTemplate.opsForValue().get(processingKey);
            String status = "未知";
            if (processingStatus != null) {
                switch (processingStatus.toString()) {
                    case "0":
                        status = "已完成";
                        break;
                    case "1":
                        status = "处理中";
                        break;
                    case "-1":
                        status = "处理失败";
                        break;
                    default:
                        status = "未知状态: " + processingStatus;
                }
            } else {
                // 如果没有处理状态，但有已完成的租户，则认为是已完成状态
                List<Object> completedTenants = redisTemplate.opsForList().range(completedTenantsKey, 0, -1);
                if (completedTenants != null && !completedTenants.isEmpty()) {
                    status = "已完成";
                    // 重新设置处理状态，确保数据一致性
                    redisTemplate.opsForValue().set(processingKey, "0");

                    // 设置过期时间到明天凌晨1点
                    Calendar tomorrowEarly = Calendar.getInstance();
                    tomorrowEarly.add(Calendar.DAY_OF_MONTH, 1);
                    tomorrowEarly.set(Calendar.HOUR_OF_DAY, 1);
                    tomorrowEarly.set(Calendar.MINUTE, 0);
                    tomorrowEarly.set(Calendar.SECOND, 0);

                    // 计算从现在到明天凌晨1点的秒数
                    long secondsUntilEnd = (tomorrowEarly.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                    // 设置Redis过期时间
                    redisTemplate.expire(processingKey, secondsUntilEnd, TimeUnit.SECONDS);
                    redisTemplate.expire(completedTenantsKey, secondsUntilEnd, TimeUnit.SECONDS);
                    redisTemplate.expire("tenant_batch_process:total_tasks", secondsUntilEnd, TimeUnit.SECONDS);
                }
            }

            // 获取初始任务总数（如果没有记录，则使用当前剩余数量）
            Object totalTasksObj = redisTemplate.opsForValue().get("tenant_batch_process:total_tasks");
            Long totalTasks = totalTasksObj != null ? Long.parseLong(totalTasksObj.toString()) : (remainingTasks != null ? remainingTasks : 0L);

            // 计算已完成的任务数
            Long completedTasks = totalTasks - (remainingTasks != null ? remainingTasks : 0L);

            // 创建统一的租户列表
            List<Map<String, Object>> allTenants = new ArrayList<>();

            // 获取当前正在处理的租户信息
            Object currentTenantObj = redisTemplate.opsForValue().get(currentTenantKey);
            if (currentTenantObj != null && "处理中".equals(status)) {
                try {
                    Map<String, Object> currentTenant = objectMapper.readValue(currentTenantObj.toString(),
                        new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                    // 添加状态字段
                    currentTenant.put("status", "处理中");
                    // 确保有租户ID和名称
                    if (currentTenant.containsKey("tenantId") && currentTenant.containsKey("tenantName")) {
                        // 将当前处理的租户添加到列表的第一位
                        allTenants.add(currentTenant);
                    }
                } catch (Exception e) {
                    log.error("解析当前租户信息失败", e);
                }
            }

            // 获取已完成的租户列表
            List<Object> completedTenantsList = redisTemplate.opsForList().range(completedTenantsKey, 0, -1);
            if (completedTenantsList != null && !completedTenantsList.isEmpty()) {
                for (Object item : completedTenantsList) {
                    try {
                        Map<String, Object> tenantInfo = objectMapper.readValue(item.toString(),
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        tenantInfo.put("status", "已完成");
                        allTenants.add(tenantInfo);
                    } catch (Exception e) {
                        log.error("解析已完成租户信息失败", e);
                    }
                }
            }

            // 获取队列中等待处理的租户列表
            List<Object> pendingTenantsList = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (pendingTenantsList != null && !pendingTenantsList.isEmpty()) {
                for (Object item : pendingTenantsList) {
                    try {
                        String configJson = item.toString();
                        DailyConfigDto dailyConfigDto = objectMapper.readValue(configJson, DailyConfigDto.class);

                        // 创建租户信息
                        Map<String, Object> tenantInfo = new HashMap<>();
                        tenantInfo.put("tenantId", dailyConfigDto.getTenantId());

                        // 尝试获取租户名称
                        String tenantName = "未知租户";
                        try {
                            // 这里可以根据实际情况从数据库或其他地方获取租户名称
                            // 如果没有可用的方法，就使用默认值
                            tenantName = "租户" + dailyConfigDto.getTenantId();
                        } catch (Exception e) {
                            log.error("获取租户名称失败", e);
                        }
                        tenantInfo.put("tenantName", tenantName);

                        // 获取点击数
                        int clickCount = 0;
                        try {
                            // 从配置中获取点击数
                            DailyConfigContent configContent = objectMapper.readValue(dailyConfigDto.getConfigJson(), DailyConfigContent.class);
                            if (configContent != null) {
                                // 获取总点击数
                                clickCount = dailyConfigDto.getClickNum() != null ? dailyConfigDto.getClickNum() : 0;
                            }
                        } catch (Exception e) {
                            log.error("获取点击数失败", e);
                        }
                        tenantInfo.put("clickCount", clickCount);

                        // 添加状态字段
                        tenantInfo.put("status", "排队中");

                        // 添加到租户列表
                        allTenants.add(tenantInfo);
                    } catch (Exception e) {
                        log.error("解析待处理租户信息失败", e);
                    }
                }
            }

            // 填充返回信息
            progressInfo.put("totalTasks", totalTasks);
            progressInfo.put("completedTasks", completedTasks);
            progressInfo.put("remainingTasks", remainingTasks != null ? remainingTasks : 0L);
            progressInfo.put("status", status);
            progressInfo.put("tenantList", allTenants);  // 统一的租户列表，包含当前处理和已完成的租户


        } catch (Exception e) {
            log.error("获取批量处理进度失败", e);
            progressInfo.put("error", "获取进度信息失败: " + e.getMessage());
        }

        return progressInfo;
    }

    /**
     * 获取点击数预生成批量处理任务的进度信息
     * @return 包含总任务数、已完成任务数、剩余任务数和处理状态的Map
     */
    public Map<String, Object> getClickPreBatchProcessProgress() {
        Map<String, Object> progressInfo = new HashMap<>();

        try {
            String redisKey = "click_pre_batch_process:configs";
            String processingKey = "click_pre_batch_process:processing";
            String currentConfigKey = "click_pre_batch_process:current_config";
            String completedConfigsKey = "click_pre_batch_process:completed_configs";

            // 获取队列中剩余的任务数
            Long remainingTasks = redisTemplate.opsForList().size(redisKey);

            // 获取处理状态
            Object processingStatus = redisTemplate.opsForValue().get(processingKey);
            String status = "未知";
            if (processingStatus != null) {
                switch (processingStatus.toString()) {
                    case "0":
                        status = "已完成";
                        break;
                    case "1":
                        status = "处理中";
                        break;
                    case "-1":
                        status = "处理失败";
                        break;
                    default:
                        status = "未知状态: " + processingStatus;
                }
            } else {
                // 如果没有处理状态，但有已完成的配置，则认为是已完成状态
                List<Object> completedConfigs = redisTemplate.opsForList().range(completedConfigsKey, 0, -1);
                if (completedConfigs != null && !completedConfigs.isEmpty()) {
                    status = "已完成";
                    // 重新设置处理状态，确保数据一致性
                    redisTemplate.opsForValue().set(processingKey, "0");

                    // 设置过期时间到明天结束
                    Calendar tomorrow = Calendar.getInstance();
                    tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                    tomorrow.set(Calendar.HOUR_OF_DAY, 23);
                    tomorrow.set(Calendar.MINUTE, 59);
                    tomorrow.set(Calendar.SECOND, 59);

                    long secondsUntilTomorrowEnd = (tomorrow.getTimeInMillis() - System.currentTimeMillis()) / 1000;

                    // 设置Redis过期时间
                    redisTemplate.expire(processingKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                    redisTemplate.expire(completedConfigsKey, secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                    redisTemplate.expire("click_pre_batch_process:total_tasks", secondsUntilTomorrowEnd, TimeUnit.SECONDS);
                }
            }

            // 获取初始任务总数（如果没有记录，则使用当前剩余数量）
            Object totalTasksObj = redisTemplate.opsForValue().get("click_pre_batch_process:total_tasks");
            Long totalTasks = totalTasksObj != null ? Long.parseLong(totalTasksObj.toString()) : (remainingTasks != null ? remainingTasks : 0L);

            // 计算已完成的任务数
            Long completedTasks = totalTasks - (remainingTasks != null ? remainingTasks : 0L);

            // 创建统一的配置列表
            List<Map<String, Object>> allConfigs = new ArrayList<>();

            // 获取当前正在处理的配置信息
            Object currentConfigObj = redisTemplate.opsForValue().get(currentConfigKey);
            if (currentConfigObj != null && "处理中".equals(status)) {
                try {
                    Map<String, Object> currentConfig = objectMapper.readValue(currentConfigObj.toString(),
                        new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                    // 添加状态字段
                    currentConfig.put("status", "处理中");
                    // 确保有租户ID和名称
                    if (currentConfig.containsKey("tenantId") && currentConfig.containsKey("tenantName")) {
                        // 将当前处理的配置添加到列表的第一位
                        allConfigs.add(currentConfig);
                    }
                } catch (Exception e) {
                    log.error("解析当前配置信息失败", e);
                }
            }

            // 获取已完成的配置列表
            List<Object> completedConfigsList = redisTemplate.opsForList().range(completedConfigsKey, 0, -1);
            if (completedConfigsList != null && !completedConfigsList.isEmpty()) {
                for (Object item : completedConfigsList) {
                    try {
                        Map<String, Object> configInfo = objectMapper.readValue(item.toString(),
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        configInfo.put("status", "已完成");
                        allConfigs.add(configInfo);
                    } catch (Exception e) {
                        log.error("解析已完成配置信息失败", e);
                    }
                }
            }

            // 获取队列中等待处理的配置列表
            List<Object> pendingConfigsList = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (pendingConfigsList != null && !pendingConfigsList.isEmpty()) {
                for (Object item : pendingConfigsList) {
                    try {
                        String configJson = item.toString();
                        Map<String, Object> configInfo = objectMapper.readValue(configJson,
                            new com.fasterxml.jackson.core.type.TypeReference<Map<String, Object>>() {});

                        // 添加状态字段
                        configInfo.put("status", "待开始");

                        // 添加到配置列表
                        allConfigs.add(configInfo);
                    } catch (Exception e) {
                        log.error("解析待处理配置信息失败", e);
                    }
                }
            }

            // 填充返回信息
            progressInfo.put("totalTasks", totalTasks);
            progressInfo.put("completedTasks", completedTasks);
            progressInfo.put("remainingTasks", remainingTasks != null ? remainingTasks : 0L);
            progressInfo.put("status", status);
            progressInfo.put("configList", allConfigs);  // 统一的配置列表，包含当前处理和已完成的配置

        } catch (Exception e) {
            log.error("获取点击数预生成批量处理进度失败", e);
            progressInfo.put("error", "获取进度信息失败: " + e.getMessage());
        }

        return progressInfo;
    }

    /**
     * 保存充值记录到数据库
     */
    private void saveRechargeRecords(DailyConfigContent configContent, Integer tenantId) {
        if (configContent.getRechargeRecords() != null && !configContent.getRechargeRecords().isEmpty()) {
            List<DailyConfigContent.RechargeRecord> rechargeRecords = configContent.getRechargeRecords();

            for (int i = 0; i < rechargeRecords.size(); i++) {
                DailyConfigContent.RechargeRecord record = rechargeRecords.get(i);
                RechargeRecode rechargeRecode = new RechargeRecode();
                rechargeRecode.setTenantId(tenantId.toString());
                rechargeRecode.setAmount(BigDecimal.valueOf(record.getConsumeAmount()));

                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Date orderDate = sdf.parse(record.getRechargeDate());
                    rechargeRecode.setOrderDate(orderDate);
                } catch (Exception e) {
                    log.error("日期解析失败: {}", record.getRechargeDate(), e);
                    continue;
                }

                rechargeRecode.setOrderNo("RO" + System.currentTimeMillis() + "_" + i);
                rechargeRecode.setCreateTime(new Date());

                rechargeRecodeService.save(rechargeRecode);
            }
        }
    }

    /**
     * 计算总充值金额
     */
    private BigDecimal calculateTotalRechargeAmount(DailyConfigContent configContent) {
        if (configContent.getRechargeRecords() == null || configContent.getRechargeRecords().isEmpty()) {
            return BigDecimal.ZERO;
        }

        return configContent.getRechargeRecords().stream()
                .map(record -> BigDecimal.valueOf(record.getConsumeAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 生成每日消耗量分配
     * 基于点击数量和单价区间，确保总金额等于充值总额，单价严格在指定区间内
     */
    private Map<LocalDateTime, BigDecimal> generateDailyUnitPriceAllocation(
            Map<String, LocalDateTime> map, BigDecimal totalAmount, Double priceStart, Double priceEnd) {

        Map<LocalDateTime, BigDecimal> dailyUnitPriceMap = new HashMap<>();

        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0 || map.isEmpty()) {
            for (LocalDateTime dateTime : map.values()) {
                dailyUnitPriceMap.put(dateTime, BigDecimal.ZERO);
            }
            return dailyUnitPriceMap;
        }

        // 1. 按日期分组统计每日的点击数
        Map<LocalDate, Long> dailyClickCountMap = map.values().stream()
                .collect(Collectors.groupingBy(
                    LocalDateTime::toLocalDate,
                    Collectors.counting()
                ));

        List<LocalDate> sortedDates = dailyClickCountMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        BigDecimal minPrice = BigDecimal.valueOf(priceStart);
        BigDecimal maxPrice = BigDecimal.valueOf(priceEnd);

        // 2. 可行性校验
        BigDecimal minTotal = BigDecimal.ZERO;
        BigDecimal maxTotal = BigDecimal.ZERO;

        for (LocalDate date : sortedDates) {
            Long clickCount = dailyClickCountMap.get(date);
            minTotal = minTotal.add(minPrice.multiply(BigDecimal.valueOf(clickCount)));
            maxTotal = maxTotal.add(maxPrice.multiply(BigDecimal.valueOf(clickCount)));
        }

        if (totalAmount.compareTo(minTotal) < 0 || totalAmount.compareTo(maxTotal) > 0) {
            log.warn("总金额{}不在可行区间[{}, {}]内，将钳制到可行区间", totalAmount, minTotal, maxTotal);
            if (totalAmount.compareTo(minTotal) < 0) {
                totalAmount = minTotal;
            } else if (totalAmount.compareTo(maxTotal) > 0) {
                totalAmount = maxTotal;
            }
        }

        // 3. 使用约束优化算法分配每日单价
        Map<LocalDate, BigDecimal> dailyUnitPriceMap_temp = allocateDailyUnitPricesWithConstraints(
                dailyClickCountMap, sortedDates, totalAmount, minPrice, maxPrice);

        // 4. 设置到每个时间点
        for (LocalDate date : sortedDates) {
            BigDecimal unitPrice = dailyUnitPriceMap_temp.get(date);
            for (LocalDateTime dateTime : map.values()) {
                if (dateTime.toLocalDate().equals(date)) {
                    dailyUnitPriceMap.put(dateTime, unitPrice);
                }
            }
        }

        // 5. 验证结果
        BigDecimal actualTotal = BigDecimal.ZERO;
        for (LocalDate date : sortedDates) {
            Long clickCount = dailyClickCountMap.get(date);
            BigDecimal unitPrice = dailyUnitPriceMap_temp.get(date);
            actualTotal = actualTotal.add(unitPrice.multiply(BigDecimal.valueOf(clickCount)));

            // 验证单价区间
            if (unitPrice.compareTo(minPrice) < 0 || unitPrice.compareTo(maxPrice) > 0) {
                log.error("日期{}的单价{}超出区间[{}, {}]", date, unitPrice, minPrice, maxPrice);
            }
        }

        log.info("单价分配完成 - 目标总额: {}, 实际总额: {}, 差额: {}",
                totalAmount, actualTotal, totalAmount.subtract(actualTotal));

        return dailyUnitPriceMap;
    }

    /**
     * 使用约束优化算法分配每日单价
     * 确保：1) 所有单价在[minPrice, maxPrice]区间内  2) 总金额精确等于目标金额
     */
    private Map<LocalDate, BigDecimal> allocateDailyUnitPricesWithConstraints(
            Map<LocalDate, Long> dailyClickCountMap,
            List<LocalDate> sortedDates,
            BigDecimal targetTotal,
            BigDecimal minPrice,
            BigDecimal maxPrice) {

        Map<LocalDate, BigDecimal> dailyUnitPriceMap = new HashMap<>();
        Random random = new Random();

        // 第一轮：按点击数比例初步分配金额，然后计算单价
        long totalClicks = dailyClickCountMap.values().stream().mapToLong(Long::longValue).sum();
        BigDecimal remainingAmount = targetTotal;

        // 为前n-1天按比例分配金额
        for (int i = 0; i < sortedDates.size() - 1; i++) {
            LocalDate date = sortedDates.get(i);
            Long clickCount = dailyClickCountMap.get(date);

            // 按点击数比例计算应分配的金额
            double ratio = (double) clickCount / totalClicks;
            BigDecimal allocatedAmount = targetTotal.multiply(BigDecimal.valueOf(ratio))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            // 计算对应的单价
            BigDecimal unitPrice = allocatedAmount.divide(BigDecimal.valueOf(clickCount), 4, BigDecimal.ROUND_HALF_UP);

            // 如果单价超出区间，调整到区间边界
            if (unitPrice.compareTo(minPrice) < 0) {
                unitPrice = minPrice;
                allocatedAmount = unitPrice.multiply(BigDecimal.valueOf(clickCount));
            } else if (unitPrice.compareTo(maxPrice) > 0) {
                unitPrice = maxPrice;
                allocatedAmount = unitPrice.multiply(BigDecimal.valueOf(clickCount));
            }

            dailyUnitPriceMap.put(date, unitPrice);
            remainingAmount = remainingAmount.subtract(allocatedAmount);
        }

        // 最后一天分配剩余金额
        if (!sortedDates.isEmpty()) {
            LocalDate lastDate = sortedDates.get(sortedDates.size() - 1);
            Long lastClickCount = dailyClickCountMap.get(lastDate);

            BigDecimal lastUnitPrice = remainingAmount.divide(BigDecimal.valueOf(lastClickCount), 4, BigDecimal.ROUND_HALF_UP);

            // 如果最后一天的单价超出区间，需要重新分配
            if (lastUnitPrice.compareTo(minPrice) < 0 || lastUnitPrice.compareTo(maxPrice) > 0) {
                // 将最后一天的单价钳制到区间内
                if (lastUnitPrice.compareTo(minPrice) < 0) {
                    lastUnitPrice = minPrice;
                } else {
                    lastUnitPrice = maxPrice;
                }

                BigDecimal lastAllocatedAmount = lastUnitPrice.multiply(BigDecimal.valueOf(lastClickCount));
                BigDecimal excessAmount = remainingAmount.subtract(lastAllocatedAmount);

                // 将多余金额重新分配给其他天（在区间约束内）
                redistributeExcessAmount(dailyUnitPriceMap, dailyClickCountMap, sortedDates,
                                       excessAmount, minPrice, maxPrice);
            }

            dailyUnitPriceMap.put(lastDate, lastUnitPrice);
        }

        return dailyUnitPriceMap;
    }

    /**
     * 重新分配多余金额，确保所有单价在区间内
     */
    private void redistributeExcessAmount(Map<LocalDate, BigDecimal> dailyUnitPriceMap,
                                        Map<LocalDate, Long> dailyClickCountMap,
                                        List<LocalDate> sortedDates,
                                        BigDecimal excessAmount,
                                        BigDecimal minPrice,
                                        BigDecimal maxPrice) {

        if (excessAmount.abs().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            return; // 金额太小，忽略
        }

        // 计算每天的可调整空间
        List<LocalDate> adjustableDates = new ArrayList<>();
        for (LocalDate date : sortedDates) {
            BigDecimal currentPrice = dailyUnitPriceMap.get(date);
            Long clickCount = dailyClickCountMap.get(date);

            if (excessAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 需要增加金额，检查是否可以提高单价
                if (currentPrice.compareTo(maxPrice) < 0) {
                    adjustableDates.add(date);
                }
            } else {
                // 需要减少金额，检查是否可以降低单价
                if (currentPrice.compareTo(minPrice) > 0) {
                    adjustableDates.add(date);
                }
            }
        }

        if (adjustableDates.isEmpty()) {
            log.warn("无法重新分配多余金额{}，所有日期的单价都已达到边界", excessAmount);
            return;
        }

        // 平均分配多余金额到可调整的日期
        BigDecimal amountPerDate = excessAmount.divide(BigDecimal.valueOf(adjustableDates.size()), 4, BigDecimal.ROUND_HALF_UP);

        for (LocalDate date : adjustableDates) {
            BigDecimal currentPrice = dailyUnitPriceMap.get(date);
            Long clickCount = dailyClickCountMap.get(date);

            BigDecimal priceAdjust = amountPerDate.divide(BigDecimal.valueOf(clickCount), 4, BigDecimal.ROUND_HALF_UP);
            BigDecimal newPrice = currentPrice.add(priceAdjust);

            // 确保新单价在区间内
            if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            } else if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            }

            dailyUnitPriceMap.put(date, newPrice.setScale(4, BigDecimal.ROUND_HALF_UP));
        }
    }

    /**
     * 校准单价使总额精确匹配目标金额（已废弃，使用新的约束优化算法）
     */
    private Map<LocalDate, BigDecimal> adjustUnitPricesToMatchTotal(
            Map<LocalDate, Long> dailyClickCountMap,
            Map<LocalDate, BigDecimal> dailyUnitPriceMap,
            BigDecimal targetTotal,
            BigDecimal minPrice,
            BigDecimal maxPrice,
            List<LocalDate> sortedDates) {

        // 计算当前总额
        BigDecimal currentTotal = BigDecimal.ZERO;
        for (LocalDate date : sortedDates) {
            Long clickCount = dailyClickCountMap.get(date);
            BigDecimal unitPrice = dailyUnitPriceMap.get(date);
            currentTotal = currentTotal.add(unitPrice.multiply(BigDecimal.valueOf(clickCount)));
        }

        BigDecimal delta = targetTotal.subtract(currentTotal);

        // 如果差额很小，跳过调整
        if (delta.abs().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            return dailyUnitPriceMap;
        }

        // 计算每日可调整空间
        Map<LocalDate, BigDecimal> adjustableSpace = new HashMap<>();
        BigDecimal totalAdjustableAmount = BigDecimal.ZERO;

        for (LocalDate date : sortedDates) {
            Long clickCount = dailyClickCountMap.get(date);
            BigDecimal currentPrice = dailyUnitPriceMap.get(date);

            BigDecimal maxAdjustableAmount;
            if (delta.compareTo(BigDecimal.ZERO) > 0) {
                // 需要增加金额，计算可上调空间
                BigDecimal upSpace = maxPrice.subtract(currentPrice);
                maxAdjustableAmount = upSpace.multiply(BigDecimal.valueOf(clickCount));
            } else {
                // 需要减少金额，计算可下调空间
                BigDecimal downSpace = currentPrice.subtract(minPrice);
                maxAdjustableAmount = downSpace.multiply(BigDecimal.valueOf(clickCount));
            }

            adjustableSpace.put(date, maxAdjustableAmount);
            totalAdjustableAmount = totalAdjustableAmount.add(maxAdjustableAmount);
        }

        // 如果总可调整空间不足，按比例调整
        if (totalAdjustableAmount.compareTo(delta.abs()) < 0) {
            log.warn("可调整空间{}不足以覆盖差额{}，将按最大可调整幅度处理", totalAdjustableAmount, delta.abs());
        }

        // 按可调整空间比例分配差额
        Map<LocalDate, BigDecimal> newPriceMap = new HashMap<>(dailyUnitPriceMap);
        BigDecimal remainingDelta = delta;

        // 按点击量从大到小排序，优先调整影响大的日期
        List<LocalDate> sortedByClicks = sortedDates.stream()
                .sorted((d1, d2) -> Long.compare(dailyClickCountMap.get(d2), dailyClickCountMap.get(d1)))
                .collect(Collectors.toList());

        for (int i = 0; i < sortedByClicks.size() && remainingDelta.abs().compareTo(BigDecimal.valueOf(0.01)) > 0; i++) {
            LocalDate date = sortedByClicks.get(i);
            Long clickCount = dailyClickCountMap.get(date);
            BigDecimal maxAdjustable = adjustableSpace.get(date);

            if (clickCount == 0 || maxAdjustable.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算本日应分配的差额
            BigDecimal dateAdjustAmount;
            if (i == sortedByClicks.size() - 1) {
                // 最后一个日期，分配剩余所有差额
                dateAdjustAmount = remainingDelta;
            } else {
                // 按可调整空间比例分配
                BigDecimal ratio = maxAdjustable.divide(totalAdjustableAmount, 6, BigDecimal.ROUND_HALF_UP);
                dateAdjustAmount = delta.multiply(ratio);
            }

            // 限制在可调整范围内
            if (dateAdjustAmount.abs().compareTo(maxAdjustable) > 0) {
                dateAdjustAmount = delta.compareTo(BigDecimal.ZERO) > 0 ? maxAdjustable : maxAdjustable.negate();
            }

            // 计算新单价
            BigDecimal priceAdjust = dateAdjustAmount.divide(BigDecimal.valueOf(clickCount), 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal newPrice = newPriceMap.get(date).add(priceAdjust);

            // 确保在价格区间内
            if (newPrice.compareTo(minPrice) < 0) {
                newPrice = minPrice;
            } else if (newPrice.compareTo(maxPrice) > 0) {
                newPrice = maxPrice;
            }

            newPriceMap.put(date, newPrice.setScale(4, BigDecimal.ROUND_HALF_UP));
            remainingDelta = remainingDelta.subtract(dateAdjustAmount);
        }

        return newPriceMap;
    }

    /**
     * 修正金额尾差，确保总额精确等于目标金额
     */
    private List<ClickAutoPre> fixAmountResidual(List<ClickAutoPre> list, BigDecimal targetTotal, Double priceStart, Double priceEnd) {
        if (list.isEmpty()) {
            return list;
        }

        // 计算当前总额
        BigDecimal actualTotal = list.stream()
                .map(ClickAutoPre::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal residual = targetTotal.subtract(actualTotal);

        log.info("尾差修正 - 目标总额: {}, 实际总额: {}, 尾差: {}", targetTotal, actualTotal, residual);

        // 如果尾差很小，跳过修正
        if (residual.abs().compareTo(BigDecimal.valueOf(0.005)) < 0) {
            return list;
        }

        // 按日期分组，优先调整点击数多的日期
        Map<LocalDate, List<ClickAutoPre>> dailyGroups = list.stream()
                .collect(Collectors.groupingBy(
                    click -> click.getClickTime().toLocalDateTime().toLocalDate()
                ));

        List<LocalDate> sortedDates = dailyGroups.keySet().stream()
                .sorted((d1, d2) -> Integer.compare(dailyGroups.get(d2).size(), dailyGroups.get(d1).size()))
                .collect(Collectors.toList());

        BigDecimal minPrice = BigDecimal.valueOf(priceStart);
        BigDecimal maxPrice = BigDecimal.valueOf(priceEnd);
        BigDecimal remainingResidual = residual;

        // 逐日修正尾差
        for (LocalDate date : sortedDates) {
            if (remainingResidual.abs().compareTo(BigDecimal.valueOf(0.005)) < 0) {
                break;
            }

            List<ClickAutoPre> dailyClicks = dailyGroups.get(date);
            if (dailyClicks.isEmpty()) {
                continue;
            }

            // 计算当前日期的平均单价
            BigDecimal dailyTotal = dailyClicks.stream()
                    .map(ClickAutoPre::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal avgPrice = dailyTotal.divide(BigDecimal.valueOf(dailyClicks.size()), 4, BigDecimal.ROUND_HALF_UP);

            // 计算可调整的金额
            BigDecimal maxAdjustPerClick = remainingResidual.compareTo(BigDecimal.ZERO) > 0
                    ? maxPrice.subtract(avgPrice)
                    : avgPrice.subtract(minPrice);

            if (maxAdjustPerClick.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 计算本日可修正的总金额
            BigDecimal maxDailyAdjust = maxAdjustPerClick.multiply(BigDecimal.valueOf(dailyClicks.size()));
            BigDecimal dailyAdjust = remainingResidual.abs().compareTo(maxDailyAdjust) <= 0
                    ? remainingResidual
                    : (remainingResidual.compareTo(BigDecimal.ZERO) > 0 ? maxDailyAdjust : maxDailyAdjust.negate());

            // 平均分配到每个点击
            BigDecimal adjustPerClick = dailyAdjust.divide(BigDecimal.valueOf(dailyClicks.size()), 4, BigDecimal.ROUND_HALF_UP);

            // 应用调整
            for (ClickAutoPre click : dailyClicks) {
                BigDecimal newAmount = click.getAmount().add(adjustPerClick).setScale(2, BigDecimal.ROUND_HALF_UP);

                // 确保在合理范围内
                if (newAmount.compareTo(minPrice) >= 0 && newAmount.compareTo(maxPrice) <= 0) {
                    click.setAmount(newAmount);
                }
            }

            remainingResidual = remainingResidual.subtract(dailyAdjust);
        }

        // 最终验证
        BigDecimal finalTotal = list.stream()
                .map(ClickAutoPre::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info("尾差修正完成 - 目标总额: {}, 最终总额: {}, 最终差额: {}",
                targetTotal, finalTotal, targetTotal.subtract(finalTotal));

        return list;
    }



    /**
     * 计算真实的单价
     */
    private BigDecimal calculateRealisticUnitPrice(Long clickCount, BigDecimal dailyAmount,
                                                  Double priceStart, Double priceEnd) {
        if (clickCount == 0) {
            return BigDecimal.ZERO;
        }

        // 计算理论单价
        BigDecimal theoreticalPrice = dailyAmount.divide(BigDecimal.valueOf(clickCount), 4, BigDecimal.ROUND_HALF_UP);

        // 确保单价在合理范围内
        BigDecimal minPrice = BigDecimal.valueOf(priceStart);
        BigDecimal maxPrice = BigDecimal.valueOf(priceEnd);

        if (theoreticalPrice.compareTo(minPrice) < 0) {
            return minPrice;
        } else if (theoreticalPrice.compareTo(maxPrice) > 0) {
            return maxPrice;
        } else {
            return theoreticalPrice.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 统计ClickAutoPre并保存日消耗明细记录
     * 确保日消耗总额等于充值总额
     */
    private void saveRechargeRecodeDetWithStats(List<ClickAutoPre> clickAutoPreList, BigDecimal totalAmount, Integer tenantId) {
        if (clickAutoPreList.isEmpty()) {
            return;
        }

        // 按日期分组统计ClickAutoPre
        Map<LocalDate, List<ClickAutoPre>> dailyClickMap = clickAutoPreList.stream()
                .collect(Collectors.groupingBy(
                    click -> click.getClickTime().toLocalDateTime().toLocalDate()
                ));

        List<LocalDate> sortedDates = dailyClickMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());

        // 计算每日权重（基于点击数）
        long totalClicks = clickAutoPreList.size();
        Map<LocalDate, BigDecimal> dailyAmountMap = new HashMap<>();
        BigDecimal remainingAmount = totalAmount;

        // 前n-1天按比例分配
        for (int i = 0; i < sortedDates.size() - 1; i++) {
            LocalDate date = sortedDates.get(i);
            List<ClickAutoPre> dailyClicks = dailyClickMap.get(date);

            // 按点击数比例分配金额
            double ratio = (double) dailyClicks.size() / totalClicks;
            BigDecimal dailyAmount = totalAmount.multiply(BigDecimal.valueOf(ratio))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);

            dailyAmountMap.put(date, dailyAmount);
            remainingAmount = remainingAmount.subtract(dailyAmount);
        }

        // 最后一天分配剩余金额，确保总和精确
        if (!sortedDates.isEmpty()) {
            LocalDate lastDate = sortedDates.get(sortedDates.size() - 1);
            dailyAmountMap.put(lastDate, remainingAmount);
        }

        // 创建RechargeRecodeDet记录
        List<RechargeRecodeDet> rechargeRecodeDetList = new ArrayList<>();

        for (LocalDate date : sortedDates) {
            List<ClickAutoPre> dailyClicks = dailyClickMap.get(date);
            BigDecimal dailyAmount = dailyAmountMap.get(date);

            // 统计当日数据
            int totalClickCount = dailyClicks.size();
            long pdCount = dailyClicks.stream()
                    .filter(click -> click.getAutoCreate() != null && click.getAutoCreate() == 1)
                    .count();

            // 计算单价（仅用于记录，不影响总金额）
            BigDecimal unitPrice = totalClickCount > 0 ?
                    dailyAmount.divide(BigDecimal.valueOf(totalClickCount), 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;

            // 创建RechargeRecodeDet记录
            RechargeRecodeDet rechargeRecodeDet = createRechargeRecodeDetWithStats(
                    date, totalClickCount, (int) pdCount, unitPrice, dailyAmount, tenantId);
            rechargeRecodeDetList.add(rechargeRecodeDet);
        }

        // 批量保存所有RechargeRecodeDet记录
        if (!rechargeRecodeDetList.isEmpty()) {
            rechargeRecodeDetService.saveBatch(rechargeRecodeDetList);

            // 验证总金额
            BigDecimal actualTotal = rechargeRecodeDetList.stream()
                    .map(RechargeRecodeDet::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("批量保存日消耗明细记录，共{}条，充值总额: {}，实际消耗总额: {}",
                    rechargeRecodeDetList.size(), totalAmount, actualTotal);
        }
    }

    /**
     * 创建日消耗明细记录对象（包含预约数统计）
     */
    private RechargeRecodeDet createRechargeRecodeDetWithStats(LocalDate date, Integer clickCount, Integer pdCount,
                                                              BigDecimal unitPrice, BigDecimal totalAmount, Integer tenantId) {
        RechargeRecodeDet rechargeRecodeDet = new RechargeRecodeDet();

        // 转换LocalDate为Date
        Date orderDate = Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant());
        rechargeRecodeDet.setOrderDate(orderDate);

        rechargeRecodeDet.setClickNum(clickCount.toString());
        rechargeRecodeDet.setPdNum(pdCount.toString()); // 设置预约数
        rechargeRecodeDet.setPrice(unitPrice);
        rechargeRecodeDet.setAmount(totalAmount);
        rechargeRecodeDet.setTenantId(tenantId.toString());

        log.debug("创建日消耗明细 - 日期: {}, 点击数: {}, 预约数: {}, 单价: {}, 总金额: {}",
                date, clickCount, pdCount, unitPrice, totalAmount);

        return rechargeRecodeDet;
    }
}
