package org.jeecg.modules.wechat.controller;

import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.demo.firmInformation.entity.WhClick;
import org.jeecg.modules.demo.firmInformation.service.IWhClickService;
import org.jeecg.modules.info.service.TenantFilter;
import org.jeecg.modules.wechat.dto.AppNewsListDto;
import org.jeecg.modules.wechat.entity.AppNews;
import org.jeecg.modules.wechat.service.IAppNewsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.wechat.vo.news.AppNewsQuikVO;
import org.jeecg.modules.wechat.vo.news.AppnewsListVO;
import org.jeecg.modules.wechat.vo.news.AppnewsVO;
import org.jeecg.modules.wechat.vo.news.AppNewsPageVO;
import org.jeecg.modules.wh.service.IWhRecordStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: app资讯
 * @Author: jeecg-boot
 * @Date:   2024-10-27
 * @Version: V1.0
 */
@Api(tags="一期-app对应pc资讯")
@RestController
@RequestMapping("/wechat/appNews")
@Slf4j
public class AppNewsController extends JeecgController<AppNews, IAppNewsService> {
	@Autowired
	private IAppNewsService appNewsService;

	 @Autowired
	 private IWhRecordStatisticsService recordStatisticsService;

	 @Autowired
	 private IWhClickService whClickService;

	/**
	 * 分页列表查询
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	//@AutoLog(value = "app资讯-分页列表查询")
	@ApiOperation(value="app资讯-分页列表查询", notes="app资讯-分页列表查询")
	@GetMapping(value = "/list")
	@TenantFilter
	public Result<IPage<AppNews>> queryPageList(
			@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
			@RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
			AppNewsListDto dto) {
		QueryWrapper<AppNews> queryWrapper = new QueryWrapper<>();
		queryWrapper.select("id,name,slogan,type,image,news_time,news_name,clicks_id,hold_num,tenant_id");
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if (StrUtil.isNotEmpty(dto.getName())) {
			//模糊查询
			queryWrapper.like("name", dto.getName());
		}

		queryWrapper.in("tenant_id", Arrays.asList(sysUser.getRelTenantIds().split(",")));
		queryWrapper.orderByDesc("create_time");
		Page<AppNews> page = new Page<AppNews>(pageNo, pageSize);
		IPage<AppNews> pageList = appNewsService.page(page, queryWrapper);
		pageList.getRecords().forEach(item->{
					if (StrUtil.isEmpty(item.getClicksId())){return;}
					WhClick whClick = whClickService.getById(item.getClicksId());
					if (whClick!=null){
						item.setClicksNum(whClick.getWhNum());
					}
				}
		);
		return Result.OK(pageList);
	}
	 /**
	  *   获取当天一个随机的实时资讯
	  *
	  * @return
	  */
	 @ApiOperation(value = "app资讯-获取一个随机的实时资讯", notes = "app资讯-获取一个随机的实时资讯")
	 @GetMapping(value = "/getNewsRandom")
	 public Result<AppNewsQuikVO> getNewsRandom() {
		 // 获取当天 type=4 的所有快讯
		 List<AppNews> newsList = appNewsService.lambdaQuery()
				 .eq(AppNews::getType, 4)
				 .like(AppNews::getNewsTime, LocalDate.now().toString()) // 只匹配到"yyyy-MM-dd"
				 .list();

		 if (CollectionUtil.isEmpty(newsList)) {
			 AppNewsQuikVO vo = new AppNewsQuikVO();
			 vo.setNewsTime("16:31");
			 vo.setContent("中航产融:子公司拟40.67亿元转让中航西飞和中航机安慰和去挖掘");

			 AppNewsQuikVO default2 = new AppNewsQuikVO();
			 default2.setNewsTime("12:20");
			 default2.setContent("五一假期结束");
			 //随机返回 vo 或者 default2
			 if (ThreadLocalRandom.current().nextBoolean()) {
				 return Result.OK(vo);
			 } else {
				 return Result.OK(default2);
			 }
		 }

		 // 聚合所有内容中的快讯条目
		 List<JSONObject> allItems = new ArrayList<>();
		 for (AppNews news : newsList) {
			 try {
				 JSONArray array = JSONArray.parseArray(news.getContent());
				 for (int i = 0; i < array.size(); i++) {
					 allItems.add(array.getJSONObject(i));
				 }
			 } catch (Exception e) {
				 // 忽略解析失败项
			 }
		 }

		 if (CollectionUtil.isEmpty(allItems)) {
			 return Result.OK(null);
		 }

		 // 随机选一条返回
		 JSONObject item = allItems.get(ThreadLocalRandom.current().nextInt(allItems.size()));
		 AppNewsQuikVO vo = new AppNewsQuikVO();
		 vo.setNewsTime(item.getString("time"));
		 vo.setContent(item.getString("title"));


		 return Result.OK(vo);

	 }


	 @ApiOperation(value = "app资讯-获取最近几日的快讯内容", notes = "app资讯-获取最近几日的快讯内容")
	 @GetMapping(value = "/getListVO")
	 public Result<List<AppnewsListVO>> getListVO() {
		 // 创建返回对象列表
		 List<AppnewsListVO> resultList = new ArrayList<>();

		 // 创建"昨天"的新闻列表
		 AppnewsListVO yesterdayList = new AppnewsListVO();
		 yesterdayList.setTopDate("昨天");
		 List<AppnewsVO> yesterdayNews = new ArrayList<>();

		 // 示例1：淘宝闪购全量上线48小时
		 AppnewsVO news1 = new AppnewsVO();
		 news1.setName("淘宝闪购全量上线48小时，饿了么13座城市物流订单突破历史峰值");
		 news1.setNewsTime("19:15");
		 news1.setHoldNum(2);

		 AppnewsVO news11 = new AppnewsVO();
		 news11.setName("淘宝闪购全量上线48小时，饿了么13座城市物流订单突破历史峰值");
		 news11.setNewsTime("19:15");
		 news11.setHoldNum(2);

		 AppnewsVO news12 = new AppnewsVO();
		 news12.setName("淘宝闪购全量上线48小时，饿了么13座城市物流订单突破历史峰值");
		 news12.setNewsTime("19:15");
		 news12.setHoldNum(2);

		 AppnewsVO news13 = new AppnewsVO();
		 news13.setName("淘宝闪购全量上线48小时，饿了么13座城市物流订单突破历史峰值");
		 news13.setNewsTime("19:15");
		 news13.setHoldNum(2);

		 AppnewsVO news14 = new AppnewsVO();
		 news14.setName("淘宝闪购全量上线48小时，饿了么13座城市物流订单突破历史峰值");
		 news14.setNewsTime("19:15");
		 news14.setHoldNum(2);

		 // 示例2：微软上调Xbox售价
		 AppnewsVO news2 = new AppnewsVO();
		 news2.setName("微软上调Xbox售价");
		 news2.setNewsTime("17:53");
		 news2.setHoldNum(2);

		 // 示例3：特斯拉视觉处理方案
		 AppnewsVO news3 = new AppnewsVO();
		 news3.setName("特斯拉：坚持视觉处理方案，让人人买得起安全智能的产品");
		 news3.setNewsTime("16:35");
		 news3.setHoldNum(55);

		 // 示例4：英伟达通报
		 AppnewsVO news4 = new AppnewsVO();
		 news4.setName("\"英伟达已向中国三家企业通报\"");
		 news4.setNewsTime("13:59");
		 news4.setHoldNum(14);

		 // 示例5：淘宝闪购开全量
		 AppnewsVO news5 = new AppnewsVO();
		 news5.setName("茱莉奶白：淘宝闪购开全量后，在饿了么订单翻倍破历史峰值");
		 news5.setNewsTime("12:30");
		 news5.setHoldNum(0);

		 // 示例6：巴菲特投资理念
		 AppnewsVO news6 = new AppnewsVO();
		 news6.setName("年轻投资者如何塑造投资理念？巴菲特：与更优秀的人为伍");
		 news6.setNewsTime("09:57");
		 news6.setHoldNum(34);

		 // 添加到昨天的列表
		 yesterdayNews.add(news1);
		 yesterdayNews.add(news11);
		 yesterdayNews.add(news12);
		 yesterdayNews.add(news13);
		 yesterdayNews.add(news14);
		 yesterdayNews.add(news2);
		 yesterdayNews.add(news3);
		 yesterdayNews.add(news4);
		 yesterdayNews.add(news5);
		 yesterdayNews.add(news6);

		 // 设置昨天的新闻列表
		 yesterdayList.setCompanyList(yesterdayNews);

		 // 创建"前天"的新闻列表
		 AppnewsListVO beforeYesterdayList = new AppnewsListVO();
		 beforeYesterdayList.setTopDate("前天");
		 List<AppnewsVO> beforeYesterdayNews = new ArrayList<>();

		 // 示例7：苹果发布会
		 AppnewsVO news7 = new AppnewsVO();
		 news7.setName("苹果发布会：iPhone 16系列正式发布，搭载A18芯片");
		 news7.setNewsTime("20:30");
		 news7.setHoldNum(78);

		 // 示例8：比特币价格
		 AppnewsVO news8 = new AppnewsVO();
		 news8.setName("比特币价格突破6万美元，创近期新高");
		 news8.setNewsTime("18:45");
		 news8.setHoldNum(42);

		 // 示例9：新能源汽车
		 AppnewsVO news9 = new AppnewsVO();
		 news9.setName("中国新能源汽车出口量同比增长125%，创历史新高");
		 news9.setNewsTime("15:20");
		 news9.setHoldNum(27);

		 // 示例10：AI芯片
		 AppnewsVO news10 = new AppnewsVO();
		 news10.setName("国产AI芯片取得重大突破，算力提升40%");
		 news10.setNewsTime("11:05");
		 news10.setHoldNum(63);

		 // 添加到前天的列表
		 beforeYesterdayNews.add(news7);
		 beforeYesterdayNews.add(news8);
		 beforeYesterdayNews.add(news9);
		 beforeYesterdayNews.add(news10);

		 // 设置前天的新闻列表
		 beforeYesterdayList.setCompanyList(beforeYesterdayNews);

		 // 添加到结果列表
		 resultList.add(yesterdayList);
		 resultList.add(beforeYesterdayList);

		 return Result.OK(resultList);
	 }

	/**
	 * 获取app_news列表 - 分页查询类型4的资讯
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@ApiOperation(value = "app资讯-获取类型4的资讯列表", notes = "app资讯-获取类型4的资讯列表")
	@GetMapping(value = "/getAppNewsList")
	public Result<IPage<AppNewsPageVO>> getAppNewsList(
			@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
			@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {

		// 创建分页对象
		Page<AppNews> page = new Page<>(pageNo, pageSize);

		// 创建查询条件
		QueryWrapper<AppNews> queryWrapper = new QueryWrapper<>();
		queryWrapper.select("id", "content", "name", "news_time", "hold_num");
		queryWrapper.eq("type", 4); // 只查询类型为4的数据
		queryWrapper.orderByDesc("news_time"); // 按照news_time降序排序

		// 执行查询
		IPage<AppNews> appNewsPage = appNewsService.page(page, queryWrapper);

		// 转换为VO对象
		IPage<AppNewsPageVO> voPage = appNewsPage.convert(appNews -> {
			AppNewsPageVO vo = new AppNewsPageVO();
			vo.setId(appNews.getId());
			vo.setContent(appNews.getContent());
			vo.setName(appNews.getName());
			vo.setNewsTime(appNews.getNewsTime());
			vo.setHoldNum(appNews.getHoldNum());
			return vo;
		});

		return Result.OK(voPage);
	}

	/**
	 *   添加
	 *
	 * @param appNews
	 * @return
	 */
	@AutoLog(value = "app资讯-添加")
	@ApiOperation(value="app资讯-添加", notes="app资讯-添加")
	@RequiresPermissions("wechat:app_news:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody AppNews appNews) {
		appNews.setType(1);
		String whClick = whClickService.savaWhClick(appNews.getClicksNum());
		appNews.setClicksId(whClick);
		appNewsService.save(appNews);
		return Result.OK("添加成功！");
	}

	/**
	 *  编辑
	 *
	 * @param appNews
	 * @return
	 */
	@AutoLog(value = "app资讯-编辑")
	@ApiOperation(value="app资讯-编辑", notes="app资讯-编辑")
	@RequiresPermissions("wechat:app_news:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody AppNews appNews) {

		appNewsService.updateById(appNews);
		if (appNews.getClicksId()!=null){
			whClickService.updateWhClick(appNews.getClicksNum(),appNews.getClicksId());
		}else {
			String whClick = whClickService.savaWhClick(appNews.getClicksNum());
			appNews.setClicksId(whClick);
			appNewsService.updateById(appNews);
		}
		recordStatisticsService.updateStatistics(appNews.getPid(), appNews.getLikeCount(), appNews.getFavoriteCount());

		return Result.OK("编辑成功!");
	}

	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "app资讯-通过id删除")
	@ApiOperation(value="app资讯-通过id删除", notes="app资讯-通过id删除")
	@RequiresPermissions("wechat:app_news:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		appNewsService.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "app资讯-批量删除")
	@ApiOperation(value="app资讯-批量删除", notes="app资讯-批量删除")
	@RequiresPermissions("wechat:app_news:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.appNewsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "app资讯-通过id查询")
	@ApiOperation(value="app资讯-通过id查询", notes="app资讯-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<AppNews> queryById(@RequestParam(name="id",required=true) String id) {
		AppNews appNews = appNewsService.getById(id);
		if(appNews==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(appNews);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param appNews
    */
    @RequiresPermissions("wechat:app_news:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AppNews appNews) {
        return super.exportXls(request, appNews, AppNews.class, "app资讯");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("wechat:app_news:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AppNews.class);
    }

}
